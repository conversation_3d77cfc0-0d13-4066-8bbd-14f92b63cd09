# 🔐 Production Environment Variables Setup Guide

## **Quick Setup (Automated)**

Run the automated setup script:

```bash
chmod +x scripts/setup-production-env.sh
./scripts/setup-production-env.sh
```

This will guide you through setting up all required environment variables interactively.

## **Manual Setup Checklist**

### **🔴 CRITICAL - Required for Deployment**

#### **Database Configuration**
```bash
export PROD_DB_HOST="your-production-database-host"
export PROD_DB_USERNAME="your-database-username"
export PROD_DB_PASSWORD="your-secure-database-password"
export PROD_DB_NAME="phcityrent_production"
```

#### **Redis Configuration**
```bash
export PROD_REDIS_HOST="your-redis-host"
export PROD_REDIS_PASSWORD="your-redis-password"
```

#### **JWT & Security Secrets**
```bash
# Generate with: openssl rand -base64 64
export PROD_JWT_SECRET="your-64-character-jwt-secret"
export PROD_JWT_REFRESH_SECRET="your-64-character-refresh-secret"
export PROD_SESSION_SECRET="your-32-character-session-secret"
```

### **🟡 IMPORTANT - Payment & Communication**

#### **Paystack (Live Keys)**
```bash
export PROD_PAYSTACK_SECRET_KEY="sk_live_your_paystack_secret"
export PROD_PAYSTACK_PUBLIC_KEY="pk_live_your_paystack_public"
export PROD_PAYSTACK_WEBHOOK_SECRET="your_paystack_webhook_secret"
```

#### **Flutterwave (Live Keys)**
```bash
export PROD_FLUTTERWAVE_SECRET_KEY="FLWSECK-your_flutterwave_secret"
export PROD_FLUTTERWAVE_PUBLIC_KEY="FLWPUBK-your_flutterwave_public"
export PROD_FLUTTERWAVE_WEBHOOK_SECRET="your_flutterwave_webhook_secret"
```

#### **Email Service (SendGrid)**
```bash
export PROD_SENDGRID_API_KEY="SG.your_sendgrid_api_key"
```

### **🟢 OPTIONAL - Enhanced Features**

#### **Google Services**
```bash
export PROD_GOOGLE_MAPS_API_KEY="your_google_maps_api_key"
export PROD_GOOGLE_ANALYTICS_ID="GA_MEASUREMENT_ID"
export PROD_GOOGLE_TAG_MANAGER_ID="GTM-XXXXXXX"
```

#### **Cloud Storage (Cloudinary)**
```bash
export PROD_CLOUDINARY_CLOUD_NAME="your_cloud_name"
export PROD_CLOUDINARY_API_KEY="your_api_key"
export PROD_CLOUDINARY_API_SECRET="your_api_secret"
```

#### **Monitoring & Analytics**
```bash
export PROD_SENTRY_DSN="https://your-sentry-dsn"
export PROD_NEW_RELIC_LICENSE_KEY="your_new_relic_key"
export PROD_NEW_RELIC_BROWSER_LICENSE_KEY="your_browser_key"
```

## **🚀 Deployment Commands**

### **Step 1: Set Environment Variables**
```bash
# Option A: Use the automated script
./scripts/setup-production-env.sh
source production.env

# Option B: Set manually
export PROD_DB_HOST="your-host"
export PROD_DB_PASSWORD="your-password"
# ... (set all required variables)
```

### **Step 2: Verify Configuration**
```bash
# Test with dry run
./scripts/deploy.sh production --dry-run
```

### **Step 3: Deploy to Production**
```bash
# Deploy to production
./scripts/deploy.sh production
```

## **🔒 Security Best Practices**

### **Environment File Security**
```bash
# Create secure environment file
touch production.env
chmod 600 production.env  # Only owner can read/write

# Add to .gitignore
echo "production.env" >> .gitignore
echo "*.env" >> .gitignore
```

### **Secret Generation**
```bash
# Generate secure JWT secret (64 characters)
openssl rand -base64 64 | tr -d "=+/" | cut -c1-64

# Generate session secret (32 characters)
openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
```

## **🎯 Service Provider Setup**

### **Database (PostgreSQL)**
- **Recommended**: AWS RDS, Google Cloud SQL, or DigitalOcean Managed Database
- **Requirements**: PostgreSQL 13+, SSL enabled
- **Configuration**: Create database and user with appropriate permissions

### **Redis Cache**
- **Recommended**: AWS ElastiCache, Redis Cloud, or DigitalOcean Managed Redis
- **Requirements**: Redis 6+, password authentication enabled

### **Payment Gateways**

#### **Paystack Setup**
1. Create Paystack business account
2. Complete business verification
3. Get live API keys from dashboard
4. Set up webhook endpoint: `https://yourdomain.com/api/v1/webhooks/paystack`

#### **Flutterwave Setup**
1. Create Flutterwave business account
2. Complete KYC verification
3. Get live API keys from dashboard
4. Set up webhook endpoint: `https://yourdomain.com/api/v1/webhooks/flutterwave`

### **Email Service (SendGrid)**
1. Create SendGrid account
2. Verify domain
3. Create API key with mail send permissions
4. Set up domain authentication

## **✅ Pre-Deployment Checklist**

- [ ] Database server running and accessible
- [ ] Redis server running and accessible
- [ ] All required environment variables set
- [ ] Payment gateway accounts verified and live keys obtained
- [ ] Email service configured and domain verified
- [ ] SSL certificates ready for domain
- [ ] DNS configured to point to server
- [ ] Backup strategy in place
- [ ] Monitoring tools configured

## **🚨 Troubleshooting**

### **Common Issues**

#### **Database Connection Failed**
```bash
# Test database connection
psql -h $PROD_DB_HOST -U $PROD_DB_USERNAME -d $PROD_DB_NAME
```

#### **Redis Connection Failed**
```bash
# Test Redis connection
redis-cli -h $PROD_REDIS_HOST -a $PROD_REDIS_PASSWORD ping
```

#### **Environment Variables Not Set**
```bash
# Check if variables are set
env | grep PROD_
```

### **Deployment Logs**
```bash
# Check deployment logs
tail -f deploy.log

# Check application logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

## **📞 Support**

If you encounter issues:
1. Check the deployment logs: `tail -f deploy.log`
2. Verify all environment variables are set: `env | grep PROD_`
3. Test individual services: database, Redis, payment gateways
4. Review the troubleshooting section above

**Ready to deploy!** 🚀
