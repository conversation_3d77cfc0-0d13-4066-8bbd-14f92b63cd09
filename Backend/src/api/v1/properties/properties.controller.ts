import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { PropertiesService } from './properties.service';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { SearchPropertiesDto } from './dto/search-properties.dto';
import { Property, PropertyStatus } from './entities/property.entity';
import { User, UserRole } from '../../../modules/users/entities/user.entity';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/auth/guards/roles.guard';
import { Roles } from '../../../modules/auth/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/user.decorator';
import { ParseUUIDPipe } from '../../../common/pipes/parse-uuid.pipe';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';
import { Public } from '../../../modules/auth/decorators/public.decorator';

@ApiTags('Properties')
@Controller('properties')
export class PropertiesController {
  constructor(private readonly propertiesService: PropertiesService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new property' })
  @ApiResponse({
    status: 201,
    description: 'Property created successfully',
    type: Property,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only landlords, agents, and admins can create properties',
  })
  create(@Body() createPropertyDto: CreatePropertyDto, @GetUser() user: User) {
    return this.propertiesService.create(createPropertyDto, user);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Search and filter properties' })
  @ApiPaginatedResponse(Property)
  @ApiResponse({
    status: 200,
    description: 'Properties retrieved successfully',
  })
  findAll(@Query() searchDto: SearchPropertiesDto) {
    return this.propertiesService.findAll(searchDto);
  }

  @Get('my-properties')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.LANDLORD, UserRole.AGENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get current user properties' })
  @ApiPaginatedResponse(Property)
  @ApiResponse({
    status: 200,
    description: 'User properties retrieved successfully',
  })
  getMyProperties(@GetUser() user: User, @Query() searchDto: SearchPropertiesDto) {
    return this.propertiesService.getMyProperties(user, searchDto);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AGENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get property statistics' })
  @ApiResponse({
    status: 200,
    description: 'Property statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalProperties: { type: 'number', example: 150 },
        availableProperties: { type: 'number', example: 120 },
        rentedProperties: { type: 'number', example: 30 },
        featuredProperties: { type: 'number', example: 25 },
        verifiedProperties: { type: 'number', example: 100 },
        occupancyRate: { type: 'number', example: 20.5 },
      },
    },
  })
  getStats() {
    return this.propertiesService.getPropertyStats();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get property by ID' })
  @ApiResponse({
    status: 200,
    description: 'Property retrieved successfully',
    type: Property,
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.propertiesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update property' })
  @ApiResponse({
    status: 200,
    description: 'Property updated successfully',
    type: Property,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - You do not have permission to update this property',
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePropertyDto: UpdatePropertyDto,
    @GetUser() user: User,
  ) {
    return this.propertiesService.update(id, updatePropertyDto, user);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update property status' })
  @ApiResponse({
    status: 200,
    description: 'Property status updated successfully',
    type: Property,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - You do not have permission to update this property',
  })
  updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status') status: PropertyStatus,
    @GetUser() user: User,
  ) {
    return this.propertiesService.updateStatus(id, status, user);
  }

  @Patch(':id/toggle-featured')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AGENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Toggle property featured status' })
  @ApiResponse({
    status: 200,
    description: 'Property featured status toggled successfully',
    type: Property,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only admins and agents can feature properties',
  })
  toggleFeatured(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    return this.propertiesService.toggleFeatured(id, user);
  }

  @Patch(':id/toggle-verified')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Toggle property verified status' })
  @ApiResponse({
    status: 200,
    description: 'Property verified status toggled successfully',
    type: Property,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only admins can verify properties',
  })
  toggleVerified(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    return this.propertiesService.toggleVerified(id, user);
  }

  @Post(':id/inquire')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Public()
  @ApiOperation({ summary: 'Record property inquiry' })
  @ApiResponse({
    status: 204,
    description: 'Property inquiry recorded successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  recordInquiry(@Param('id', ParseUUIDPipe) id: string) {
    return this.propertiesService.incrementInquiries(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Delete property' })
  @ApiResponse({
    status: 204,
    description: 'Property deleted successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - You do not have permission to delete this property',
  })
  @ApiResponse({
    status: 404,
    description: 'Property not found',
  })
  remove(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    return this.propertiesService.remove(id, user);
  }
}
