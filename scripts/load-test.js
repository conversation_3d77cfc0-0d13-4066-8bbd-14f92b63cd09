// Load Testing Script for PHCityRent
// Uses k6 for performance testing

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');
const requestCount = new Counter('requests');

// Test configuration
export const options = {
  stages: [
    // Ramp-up: gradually increase load
    { duration: '2m', target: 10 }, // Ramp up to 10 users over 2 minutes
    { duration: '5m', target: 10 }, // Stay at 10 users for 5 minutes
    { duration: '2m', target: 20 }, // Ramp up to 20 users over 2 minutes
    { duration: '5m', target: 20 }, // Stay at 20 users for 5 minutes
    { duration: '2m', target: 50 }, // Ramp up to 50 users over 2 minutes
    { duration: '5m', target: 50 }, // Stay at 50 users for 5 minutes
    { duration: '2m', target: 0 },  // Ramp down to 0 users over 2 minutes
  ],
  thresholds: {
    // Performance thresholds
    http_req_duration: ['p(95)<2000'], // 95% of requests should be below 2s
    http_req_failed: ['rate<0.05'],    // Error rate should be below 5%
    errors: ['rate<0.05'],             // Custom error rate should be below 5%
  },
};

// Base URL configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:4173';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
];

// Helper function to generate random data
function randomString(length) {
  const chars = 'abcdefghijklmnopqrstuvwxyz';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Main test function
export default function () {
  const testScenario = Math.random();
  
  if (testScenario < 0.4) {
    // 40% - Browse properties (most common user behavior)
    browseProperties();
  } else if (testScenario < 0.7) {
    // 30% - Search and filter properties
    searchProperties();
  } else if (testScenario < 0.9) {
    // 20% - User authentication flow
    authenticationFlow();
  } else {
    // 10% - Contact agent flow
    contactAgentFlow();
  }
  
  // Random sleep between 1-3 seconds to simulate user behavior
  sleep(Math.random() * 2 + 1);
}

// Test scenario: Browse properties
function browseProperties() {
  const responses = http.batch([
    ['GET', `${BASE_URL}/`],
    ['GET', `${BASE_URL}/properties`],
  ]);
  
  responses.forEach((response, index) => {
    const isSuccess = check(response, {
      [`Page ${index + 1} status is 200`]: (r) => r.status === 200,
      [`Page ${index + 1} response time < 2s`]: (r) => r.timings.duration < 2000,
    });
    
    errorRate.add(!isSuccess);
    responseTime.add(response.timings.duration);
    requestCount.add(1);
  });
  
  // Simulate viewing property details
  const propertyDetailResponse = http.get(`${BASE_URL}/properties`);
  
  check(propertyDetailResponse, {
    'Property details loaded': (r) => r.status === 200,
    'Property details response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(propertyDetailResponse.status !== 200);
  responseTime.add(propertyDetailResponse.timings.duration);
  requestCount.add(1);
}

// Test scenario: Search properties
function searchProperties() {
  const searchParams = {
    location: 'Port Harcourt',
    minPrice: '50000',
    maxPrice: '200000',
    bedrooms: '2',
  };
  
  const searchUrl = `${BASE_URL}/properties?${Object.entries(searchParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&')}`;
  
  const response = http.get(searchUrl);
  
  const isSuccess = check(response, {
    'Search results loaded': (r) => r.status === 200,
    'Search response time < 3s': (r) => r.timings.duration < 3000,
    'Search results contain data': (r) => r.body.length > 1000,
  });
  
  errorRate.add(!isSuccess);
  responseTime.add(response.timings.duration);
  requestCount.add(1);
  
  // Simulate applying filters
  const filterResponse = http.get(`${BASE_URL}/properties?type=apartment`);
  
  check(filterResponse, {
    'Filter results loaded': (r) => r.status === 200,
    'Filter response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(filterResponse.status !== 200);
  responseTime.add(filterResponse.timings.duration);
  requestCount.add(1);
}

// Test scenario: Authentication flow
function authenticationFlow() {
  // Test sign-in page load
  const signInPageResponse = http.get(`${BASE_URL}/auth`);
  
  check(signInPageResponse, {
    'Sign-in page loaded': (r) => r.status === 200,
    'Sign-in page response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(signInPageResponse.status !== 200);
  responseTime.add(signInPageResponse.timings.duration);
  requestCount.add(1);
  
  // Simulate sign-in attempt (this would normally be a POST request)
  const testUser = testUsers[Math.floor(Math.random() * testUsers.length)];
  
  // Note: In a real scenario, you would make a POST request to your auth endpoint
  // const signInResponse = http.post(`${BASE_URL}/api/auth/signin`, {
  //   email: testUser.email,
  //   password: testUser.password,
  // });
  
  // For now, we'll simulate the auth flow with a GET request
  const authResponse = http.get(`${BASE_URL}/`);
  
  check(authResponse, {
    'Auth flow completed': (r) => r.status === 200,
    'Auth response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(authResponse.status !== 200);
  responseTime.add(authResponse.timings.duration);
  requestCount.add(1);
}

// Test scenario: Contact agent flow
function contactAgentFlow() {
  // Load contact page
  const contactPageResponse = http.get(`${BASE_URL}/contact`);
  
  check(contactPageResponse, {
    'Contact page loaded': (r) => r.status === 200,
    'Contact page response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(contactPageResponse.status !== 200);
  responseTime.add(contactPageResponse.timings.duration);
  requestCount.add(1);
  
  // Simulate form submission (normally would be POST)
  const formData = {
    name: `Test User ${randomString(5)}`,
    email: `test${randomString(5)}@example.com`,
    message: `Test message ${randomString(20)}`,
  };
  
  // Note: In a real scenario, you would POST to your contact endpoint
  // const submitResponse = http.post(`${BASE_URL}/api/contact`, formData);
  
  // For now, simulate with another page load
  const submitResponse = http.get(`${BASE_URL}/`);
  
  check(submitResponse, {
    'Contact form processed': (r) => r.status === 200,
    'Contact form response time < 3s': (r) => r.timings.duration < 3000,
  });
  
  errorRate.add(submitResponse.status !== 200);
  responseTime.add(submitResponse.timings.duration);
  requestCount.add(1);
}

// Setup function (runs once at the beginning)
export function setup() {
  console.log('🚀 Starting PHCityRent Load Test');
  console.log(`📊 Target URL: ${BASE_URL}`);
  console.log('📈 Test Scenarios:');
  console.log('  - 40% Browse Properties');
  console.log('  - 30% Search Properties');
  console.log('  - 20% Authentication Flow');
  console.log('  - 10% Contact Agent Flow');
  console.log('');
  
  // Verify the application is accessible
  const healthCheck = http.get(`${BASE_URL}/`);
  
  if (healthCheck.status !== 200) {
    console.error(`❌ Health check failed: ${healthCheck.status}`);
    console.error('Make sure the application is running before starting the load test');
    return null;
  }
  
  console.log('✅ Health check passed - Application is accessible');
  console.log('🔄 Starting load test...');
  console.log('');
  
  return { baseUrl: BASE_URL };
}

// Teardown function (runs once at the end)
export function teardown(data) {
  console.log('');
  console.log('🏁 Load Test Completed');
  console.log('📊 Check the results above for performance metrics');
  console.log('');
  console.log('Key Metrics to Review:');
  console.log('  - http_req_duration: Response times');
  console.log('  - http_req_failed: Error rates');
  console.log('  - errors: Custom error tracking');
  console.log('  - requests: Total request count');
  console.log('');
  console.log('Thresholds:');
  console.log('  - 95% of requests should be < 2s');
  console.log('  - Error rate should be < 5%');
  console.log('');
}

// Handle different test environments
export function handleSummary(data) {
  return {
    'load-test-results.json': JSON.stringify(data, null, 2),
    stdout: `
📊 LOAD TEST SUMMARY
====================

Duration: ${data.metrics.iteration_duration.avg.toFixed(2)}ms average
Requests: ${data.metrics.http_reqs.count} total
Errors: ${data.metrics.http_req_failed.rate.toFixed(4)} error rate
Response Time (avg): ${data.metrics.http_req_duration.avg.toFixed(2)}ms
Response Time (95th): ${data.metrics.http_req_duration['p(95)'].toFixed(2)}ms

🎯 PERFORMANCE THRESHOLDS
========================
✅ 95th percentile < 2000ms: ${data.metrics.http_req_duration['p(95)'] < 2000 ? 'PASS' : 'FAIL'}
✅ Error rate < 5%: ${data.metrics.http_req_failed.rate < 0.05 ? 'PASS' : 'FAIL'}

🚀 RECOMMENDATIONS
==================
${data.metrics.http_req_duration['p(95)'] > 2000 ? '⚠️  Consider optimizing response times' : '✅ Response times are within acceptable limits'}
${data.metrics.http_req_failed.rate > 0.05 ? '⚠️  High error rate detected - investigate server issues' : '✅ Error rate is within acceptable limits'}

`,
  };
}
