{"lighthouseVersion": "12.6.1", "requestedUrl": "http://localhost:4173/properties", "mainDocumentUrl": "http://localhost:4173/properties", "finalDisplayedUrl": "http://localhost:4173/properties", "finalUrl": "http://localhost:4173/properties", "fetchTime": "2025-07-18T12:57:48.954Z", "gatherMode": "navigation", "runWarnings": [], "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2938, "credits": {"axe-core": "4.10.3"}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "notApplicable"}, "viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1.0"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 0.01, "scoreDisplayMode": "numeric", "numericValue": 6905.0301, "numericUnit": "millisecond", "displayValue": "6.9 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.03, "scoreDisplayMode": "numeric", "numericValue": 7776.0301, "numericUnit": "millisecond", "displayValue": "7.8 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 0.33, "scoreDisplayMode": "numeric", "numericValue": 6905.0301, "numericUnit": "millisecond", "displayValue": "6.9 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3000, "items": [{"timing": 375, "timestamp": 227735960187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 750, "timestamp": 227736335187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1125, "timestamp": 227736710187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1500, "timestamp": 227737085187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1875, "timestamp": 227737460187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 2250, "timestamp": 227737835187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 2625, "timestamp": 227738210187, "data": "data:image/jpeg;base64,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"}, {"timing": 3000, "timestamp": 227738585187, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAAAAgIDAQEAAAAAAAAAAAAAAAECBAMFBwYI/8QAUBAAAQMCAgUFDQcCBAMFCQAAAQACAwQRBRIGEyExURRBgZLRBxYiM1JTVWFxcpGUsRUyNpOhsuIXIyQ0QsE1c4JDVmJ08ERFY2SWs9LT8f/EABsBAAMAAwEBAAAAAAAAAAAAAAABAgMEBgUH/8QAOREAAgECAgYIAwkBAAMBAAAAAAECAxEEMRITIVFSoQUGFTRBcsHRFmFxFCIyQlOBkbHw4SMzYvH/2gAMAwEAAhEDEQA/APqlCF5/GdLsLwjETQ1bp9e1gkcI4XPDWnnNgonUjTV5uyLp0p1XowV38j0CFp4tJMMmkwtsFRrRiWfkzmtNnZPvX4W9av4nX0uGUMtZXzNhpohdz3bhzIVSLTaexf8A6N0pxai4u797f3sLKF57AtMcHxqq5LSTSNqS3OyOWNzC9vFtxtVqix+jqIqAziainrS8Q09UwskcWnbs/X2FKNWEldMqeHqwejKLT+ht0IXmMX04wbCcVnw+sfUCeANdKY4HvawEAgkgHZYqpTjBXk7E0qNSs9GnFt/I9OhVsOrqbEqGGsoZmzU0zczJG7iFZVJ32ohpxdmCFTxXE6PCaN1XiNQynp2kNMj91ybD9VcRfwDRdtK2wEIQgQIQhAAhCEACEIQAIQhAAhCEACEIQAIQsdTUQ0tPJUVUscMETS98kjg1rGjeSTsAQBkQvP8Afror/wB5cE+fi/8AyW6oqunrqWOpop4qimkF2SwvD2OHEEbCgDMhCEAC5zibcTd3U6gYM+lZU/Zzbmpa4ty5hewHPuXRkLDWpa1JXtZ3M9CtqXJ2vdWOWYho1Fh2J6GYRUTPmD31bpnsJZmLmtJAsbgc2zmWprKSpGj9fFCJ6mgwjHczoS4vIgaNrRfaQCf912lC1/sMVfRdr+yXpc3V0pN6Okr2+eb0m7/LOx46PSTA8cxqjjw2E19RFHK8VTYyBSgt5yQPvbrBc2w6mw84ZoRXYuCKd8lTFUTPe4Cwe4sBIOzaSu8taG/dAHPsTIBFiAQqnhXUd5NX+nzXz+QUOkI0E404u3m25SWdv/q+Xgci0obVYZjeL6N0OsY3SCWGSnkuSI8xtN9L+wrDiMOL0Gm2krNF3QNkpsPhbq5Iy9zmiNoAbzX9t10hujkB0mGNz1NTPUMaWQxyOGrgBAByADeQOcneVu0LDNttu23Z9Nvqy+0lCKUYp/ds75XvH+dkUvrdnCKtzWYDodBHURHAHQyukdVvfHGZySS2Qx7RYk2F7K7S089TBofSVGJPqqSWuqWRyQukb/ZyfczOAcR94X4bjw7S5oc0tcAQeYpqlhreO7lb2HLpW6/Dt+9479Lbln97Pxt/Hz9jVEwaKaW0VpZaXCsVYaYOe52pY51nDfutx9u9dzwLkP2PR/ZDmuw/VjUFri4FnNtO09KvWQAAAALAbgFlp0tB3NbFY54mCg1azvnvST5q/wC7BCELKaAIQhAAhCEACEIQAIQhAAhCEACEIQAKnjEwp8JrJjLHCI4nOMksRlayw3lgILh6gRdXEIA5X30w/wDefAP/AKdqP/3Loej1QKvBaSdtRDUh7L62GB0LHbTtDHElvsJK2KE2xJAhCEhmN1RC370rB7So8qp/PR9YKpJCHbwsRphwTsBsOVU/no+sEcqp/PR9YLXcmHBHJmpAbHlVP56PrBHKqfz0fWC13Jmo5M1AGx5VT+ej6wRyqn89H1gtdyZqXJmoA2XKqfz0fWCOVU/no+sFreTBHJh6kAbLlVP56PrBHKqfz0fWC1vJh6kcmHqRYDZcqp/PR9YI5VT+ej6wWt5MEcmCdgNlyqn89H1gjlVP56PrBa3kw4I5MOCLAbLlVP56PrBHK6fz0fWC1vJhwS5MOASA2fK6fz0fWCOV0/no+sFrOTDgjkw4IA2fK6fz0fWCOV0/no+sFrOTDgjkw4IA2fK6fz0fWCOV0/no+sFrOTDgjkw4J2A2fK6fz0fWCOV0/no+sFrOTDgjkw4IsBs+V0/n4+sEcrp/Px9YLWcmHBLkw4IsBtOV0/n4+sEcrp/Px9YLV8mHBHJhwSA2oqoDumjP/UFLXR+W34rVsgAO5ZsidgLVkiAOZM7k4hck8NiAARX+98FLVM8kJyPEbczlT5e3Xau7M9s2S/hW4rHKpGOZUYOWRb1bPJb8Eatnkt+Ccbw9ocE7jirTuSR1bPJb8Eatnkt+CmhAENWzyW/BGrZ5LfgpoQBDVs8lvwRq2eS34KRIG82TQBDVs8lvwRq2eS34KaEAQ1bPJb8FF0LTuFismYZstxmteyaAKzmZTYgIyjgs8jczSOfmWBpuEwDKOCMo4JoQBHKOCMo4KSEARyjgjKOCkhAEco4IyjgmhACy+pLKOCkhAiOUcEZQpIQMjlCdk0IAk7cp05vH0qDtyx08mSQsduduQBKtBytPMvBxyTnJG+nnFTcOLxTvzCaw2591t44WFty6I4BwIIuCsPJmXvttwWnXw7qO6ZtUMQqSs0RogcrjzE7F5GPA8ajJkikAtitRVMhcGZWtdrMryb33ubs37dy9sAGgACwTW1BaKsa0npO54duH6RTULotdiEVyHXfUM1ufUSZrOBPgGTV2Ht2BqrVmH6XASRU1TUiAPc9j87HyZjFFbe9oyiTXeCTbdssugoVXJsJt8ozb7bVUo4po6mqe85YHuuyMnMQed1+YHh086uISGed0uwefGJcNZDHTOjjke6R1REJWNBYQDluLm6oPwjFqZro4qiump45IogI5mte+ERNDnC52OLxtub2vbft9ihYXQi5OXizYjiZRio7LL3ueAw8aQy4vkdJUmop3QCW8jNU1pj8PM2+0nZuG/htWTCcM0ke/D2YjUVbYtfnqSJGNIGqdexDyS0vybBa3MLL3aEo0LZyZkljG7pQSut3yObRYFpFSw5KQVAcIxE+V8zXyOGucTlOYH7pB3jn51tsHpNIYscw2SukqpqUU7Y5s7mMa12V93ENe7MScoOz1h1rhezQqjSUcmwnjZTTTitt/DeCpsNyfas1VKI4zt8I7AsEQNlmNMyoQhAAhCEACEIQAkI2oQAIQhAgQhCABCEIGTO5Vp47qyk4XQBVjrHxeDK0vA5xvWX7Rp+cuH/SUnxX5liMAPMgDN9o0/lO6pR9o0/lO6pVfk44I5OOCLAWPtGn8p3VKPtGn8p3VKr8nHBHJxwQBY+0afyndUo+0afyndUqvyccEcnHBAFj7Rp/Kd1Sj7Rp/Kd1Sq/JxwRyccP0QBY+0afyndUo+0qfyndUqvyccEuTjh+iALP2jT+U7qlQfiLTshY5x4nYFi5OOCm2EDmQBBgfI/PIblWmCwSayymgAQhCABCEIAEISQAdKEdCEACEIQIEIQgYIQhAE0IWq0kxymwKg5RUXe92yOIGxef8Ab2qKlSNOLnN2SLp05VJKEFds2tkrLi+KaZ4zXPdlqeTRE7GQjLYe9vWsOPYt6SrPzndq8WXT9BO0YtnvQ6uYiSvKSX8ne7IsuB/b2Lek6z853al9vYt6TrPzndqS6fpcD5FfDVfjXP2O+2RZcCOPYt6Trfzndqj9v4v6Trfzndqfb1LhfIfw1X41z9jv9krLgP2/i/pOt/Od2qJx/F/Slb+c7tVLpylwsPhmvxrn7H0BZFl8/HH8Xt/xSt/Od2pd8GL+lK3853an23S4WHwzX41z9j6Csiy+fTpBjHpSt/Pd2qJ0gxj0pW/nO7U+2afCx/DFfjXP2PoSyLL5674cY9KV357u1I6Q4x6Urvz3dqrtinwsPhjEca5+x9DIXzx3w4z6Vrvz3dqR0hxn0rXfnu7U+16fCw+GMRxrn7H0QhfOx0hxn0rXfnu7Uu+LGfStd+e7tT7Vp8LH8L4jjXP2PopC+c++LGfStd+e7tSOkWM+la7893aq7Up8LD4XxHGufsfRqF85HSLGbf8AFa7893ao98WNelq7893an2nDcx/C+I41z9j6OQvnDvixr0tX/nu7Ud8eNelq/wDPd2p9pQ3MPhbEca5+x9HoXzd3x416Wr/mHdqR0kxr0tX/ADDu1V2hDcw+FsRxx5+x9JIXzvQaZY/QyZ4sTnk4tmOsB6111TQPTeLSL/CVbGwYi1t8rT4MoG8t4exZqWLhUdsmaOO6CxODhrHaUfG3ge0QhC2Txia493SqySo0nmhc7+3Ttaxo4XAJP6/ouwrimn34uxD3m/savD6fk1hkl4v0Z7/V2KeKbfhF/wBo88kU1ErkUdshFLnTKXOrQ0JyRTckVaGJJyai5WhiKSZ3JLIhiKRTKR9qpDIpFNIrIhiSKaiVaGIpJlJWhoRSKCgqkMSipHcoq0AFJMpK0UR50imkVaASs4XWy4diVNWQOyyQyB4PsO0dI2KsorJF2d0KUVOLjLJn1Qx2ZjXDnF1JY4PER+6Posi6I+QMmuKaffi7EPeb+xq7WuKaffi7Efeb+xq8LrB3ePm9GdB1b7zLy+qPPLdaF08NVpRQQ1MTJYXudmY8XB8E7wtKtvojWQYfpHRVVZJq4I3OLnWJt4JG4bedcxhWlWg5ZXX9nW4tSdCoo52f9G70jlqKaXEIWaMUcVKxz421HInCzdwcHbulFTgNVilTo9RyT00evpCY3Rw2LWht7O27StLj+OVlbX1zY6+okopZXZGF7spZfZsPNu2L09HpFhUeL6NTvqrRUdI6Kd2rf4Diy1t23bwXsKpRrTkpS2XWdl+bbl4WPEVHEYenCUI/etJ7E3t0dl73s7miqtEnswutqqbEKaplov8AMQx3uzp5zsPwKjS6KuOGUdXV19LTSVm2mgkveTp5r7PiFnwfF6Kmo9KY558rq1hFOMjjnN38Bs3jfZWaquwTGMFwd1dXyUtTh8YjkhbCXGUC33TuBNv1UxpYaS0la9sr/O2f02meVbGxloSva62qN3tje1rZX2X8N5r+6PR09DpPJBRwRwRCJhyRtDRe3AK7R4VT6T6M04w2nghxajkbHOGMDTKxxsHm3Df0Fa7T/EqTFdI5Kqgl1sBjY0Oylu0DbsIBUu5/itNg+OyT105hgdA9l8rnXdcW2AFCnTeKnF20JNr/AKi3SrLAU5wvrIJNZ3+aa+htMXwSlxPFHYZg7aWnosLi/wAVXOaAXOttLiN9rHputJiejDqaCjq6SthraCplEInjBGVxNtrSs+h+L0dNT4tQYpK+GHEYshqAC7I7btIG0/e/RWcSxTDcL0dpcJwuqNfIKoVMs2rLGix2AA+wfqsr1FWDqSsnt8ctuxW+nyIj9rw9RUKd2k14bGmryk3vv4X/AGKPepL34DAOVM1hF9dk2fcz7r9CWGaKmtw+rrZK6Cmp6WpNPK+UWDQAPC38SBZeqGNaOd+UGkBxOQF7MroDTu/tnIW3JttHNYX2ngvMyYrRnRDFaITf4mfEDPGzK7wmeDtva3Nu3qnRw8Lt2f4rbfDZYaxONqaCimtkU3o+Lb0ntX0+RQ0apIH6X0dLJq6qm5TkuW3bI2522PMd69th9NRYrpRiGDVGjtHFRs1jW1MEOrcy24l27avA6LVUNDpDQVNU/VwRShz3WJsPYNq22k2mOKV1ZWw02JSHDnyOEYY0Muy+zbYO3cUYatTp0ry35bHst8/AvHYSviMQow4c22rO+ezx+RXodGGS0MlfXYlBQ0BmdDDLI0uMpBO0Ac2xZabQqsm0ilwh88TZG05qY5WjM2RtwBbdvv8Aotxo3pLC3RWnwwYsMJqqaRzhI+mEzZWEk23GxuVPD9J6KPTCerrcVlqaVtE6njqJKcMLnEg2DWN3b94WWFLDNRv428f58fQx1cR0hGVRRWV7bHvVmvu22/V/RWMehGjmFd9MUc+J0le6KPWalkZc1ztoIvu8GwPSFr6/Cn6R4ziVa7EKNmHUjW62sbCWRjg0N3k/+uCpdzzE6TCNJY6vEZtTTiJ7S7KXbSNmwAlbHQjSODDqHFcPmrOQOqXiWGr1OtDHcC2x3gD9U6cqU4Rg7JXfj8tniVXpYqlVnVg3JpRSdt722snl++fjsNRiGidVFPhow+aKvp8QJbTzRXAcRvBB2i3+x4KziWhzaenrTS4vR1VXRNzVFO27S0c9id9v/W1bes0tbR4vg032tJjDKWRz5bUzYWNzAtOSwBJsTv2bPhrscg0Xc/E8QZi09XPUl0lPTxwuYWPcb+ESLWF/gm6dGz0f7y2eG1+P1LhiMbeKndK3hG93pZO6Vtnl3/Iy6SaP4PR6GYVXUtXFyqRryXhj/wDFHMBYX2Ny/qvCr2OIV2G4joFhdKa4Q4jh2s/w7onHW5ncztw2bV45Yq2jpJxSyWX0PR6P1ipyVVttSlnuvsts2q3t8gSTKSxo9EikU0irQCSTUVaGfU0HiI/dH0WRY4PER+6Posi6NHx55k1xTT78XYj7zf2NXa1xTT78XYh7zf2NXhdYO7x83ozoOrfeZeX1R55Tp4JamZsNPG+WV33WMFyehQW/0B/F+He879hXLUKaqVIwfi0jr8RUdKlOovBN/wAI0Rp5uU8n1T9fn1ert4Wa9rW43UZ4ZKeZ0U8bo5WGzmuFiF0J2C4P33io74YuU8tEnJ+Tm+bPfJe/HZdYarC6GbG9JcVxVj5qahkH9hjsusc4bLnm5vivRfR0knt8Ws1kk3d2vuPOh0tFyV07aKeTvdtKyva+Zz5yRXuZsFwuup8ExSgp309PU1baaeldIXAeFa4dv5v1V92BaPT4ri+EspaiGamZrxUCQusAAS0NPt5786I9HVHk1ty+ey+7+y5dMUY3Ti9meWyztt2791zmyi5eyxTDcKxDR6hxDBqV9HI6rFLIx8hfe42G56OG8r0j9CqAVX2acMmEWr/4pylt89r+Lvu5t3arh0dVk/utNbNu3x/b+xz6YoQX3k09t1surZ+P9XbOUHckstVC6nqJYX2zxvLHW4g2WJalrbGetFqSuhFI2TKRVIoikU0irQxKJUkisiGRKSZSVoaIlIqRSKpDEdyipHcoq0AFJMpK0URST50irQCUVJJWhn1LB4iP3R9FkWODxEfuj6LIujR8feZNcU0+/F2I+839jV2tcU0+/F2Ie839jV4XWDu8fN6M9/q33mXl9UeeV/AMS+yMYpq7Va7UknJmy3uCN9jxVBRK5SnNwkpRzR2c4RqRcJZPYbF2J30hGKar/wBpFRqs3/izWvb9bLa0+ljo8UxOaaiZNQ4gf71K5/ws63+y8wUudbMMVVhtT8b/ALmCWCoTVpR8LZvJbf8APM9PXaVNfJhsVDQNpMOoZhO2mbIXF7gb7XEe3486izSrJj+J4nyO/LYTFq9b9y4Ave23dwC8y5IrL9trXvpfPJbrf0R2dhrNaOatm9u2+/f45m2ZjTo9HG4WyGzm1IqRMH7iBa1rfrdbWs0roa0uqq7AKafFHMyundI7I7ZYEx7v16V5NRcnDFVYq193gvDIuWAoSd2tt282s88n42yyE433pJncksaN0RSKZSKpDIpFNIrIhiUSpKJVoYikmUlaGRKCgoKpDEdyipHcoq0AFJMpK0URCRTSVoBKKkkrQz6lg8RH7o+iyLHB4iP3R9FkXRo+PvMmuKaffi7EPeb+xq7WuKaffi7EPeb+xq8LrB3ePm9Ge/1b7zLy+qPPJFC9P3OcOhxPSeNlS0PjhjdMWEbHEEAfqQehcvh6Lr1I045s67EV44elKrLJI0sOD4nPG2SHD6t8bhcObC4g/opfYOL+i638h3YvoVUZMWpGPcHPfkYS10ojcY2kbCC61h8V0y6Ap8bOVfWWtfZBczhDsBxf0XXfkO7FE4Di/ouu/Id2L6HVasxCjoXRNraunp3SnLGJZAzOeAvvVLoKlxMXxLX4Fz9zgP2BjHouu/Id2KJwDGPRdd+Q7sX0ShNdB0uJj+Jq/AufufOp0fxj0VXfkO7EvsDGPRVd+Q7sX0WoQzRTx54JGSMuW5mG4uCQR0EEdCrsWnxMPiavwLn7nzsdH8Y9FV/y7+xI6P4zzYVX/Lv7F9FMljfJJGyRjnxkB7QbltxcXHNsU0+xqfEx/E9fgXP3PnDvfxn0VX/Lv7EHR7GfRVf8u/sX0XLPFCG62RjMzgwZja7juHtKlrY9Y2PO3WOBcG32kC1zb1XHxCfY9PiYfE9fgXP3PnHvexn0TX/Lv7Ejo9jXomv+Xf2L6RURIwyujD2mRoDi2+0A3sbeux+BVdk0+Jh8T4jgXP3Pm86PY16Jr/l39iXe7jXomv8Al39i+k0J9lU+Jj+KMRwLn7nzV3u416Jr/l39iR0dxr0RiHy7+xfSTp4mzRxOlYJZL5GFwu62+w57LIn2XDiYfFGI4Fz9z5pOjuNeiMQ+Xf2KPe5jfojEPl39i+mEJ9mw3sPijEcEefufM/e5jfojEPl39iXe5jfojEPln9i+mVEyMErYy9okcC4NvtIFrm3quPiFXZ0N7H8U4jgjz9z5mOjmNgXOEYhYf/LP7Fq5WPje5kjHMe02LXCxB9YX1euU93HCqdlPQ4qxjW1DpeTyED74yki/syn4rHWwSpwcovI3+jusUsTXVGrFLSya3nI0k1FaKOrPqaDxEfuj6LIscHiIvdH0WRdGj488yfQuKaffi7EPeb+xq7X0rimn34uxD3m/savC6wd3j5vRnQdW+8y8vqjzpXs+5J+KZv8Ayj/3MXjCtporjRwDHIa0sL4rFkjRvLTw6bHoXO4CpGliITlkmdP0jRlWw06cM2j6AcLgi9r860cE8tJhIo30E81RFHqgxrLslsLXzbgDv2m6KfS3R+eISNxeiaDzSShh+DrFZO+jAfTWHfMs7V3SrU2rqS/k+eSo1E7OL/gu4Q5pwulDH58sYYXEWNwLG45jcFaTSWMNrXzRsndM6m1eQ0ZqIZxdxyEAXaekAh3PbZe76MA9NYb8yztR30YB6aw35lnaqVWG9E6qe5mlvjDse/ua2nYJodUxglcwRZWZ2+D4G/OLu2jm5lV1uLTOrTEzEITLTTEs/uudHIHsygEjKDYusGC1hz7F6TvowD01hvzLO1HfTgHprDfmWdqeuhvQtTPcyjNE+DFnxSvxfVsMXJTC572m58PMdoO3fn3NtZYNHKOqpJKKQisYJamsE0by7IGmV7mOynYOax583Otp304B6bw35lnajvpwD03hvzLO1GtjvQ9VPczS10dTHjmMSU7MQbVSSQGlLGyal5DWh2YjwSOY5tw3bVfwh9ZJj09FK6fk+HOe7WOkJ12s8KMHiGtLhY84BVvvq0f9N4b80ztWCmx/RilMpp8WwmMyvMshbUMBe47ydu0o1sd4aqe5leWklhxavMTawPnq4H3DpCwx2YHW/wBI3G/OB6lr4qOuo8OjZh0VUKmGmqmEy53EOMsR2F173aCRbYbbOdb/AL6tH/TeGfNM7Ud9Wj/pzDPmmdqNbHeGpnuZX0bmmg1rK2aUxzTBlO2Rkt75C5wvJ4RHgk7dg2hLGmVbcQqpKdk4hcykbK+FpzGMSS5w2229iL222OzbZZn6T6OOcxz8awouYczSalngmxFxt4EjpUu+vR705hnzTO1GsjvDVT3M1TDVZmCqOJjCNbLq3MEuuIysyZrf3LX1u/g26x4jNiPLaPk7MRBZyYXkEhL2lzc5c1g1YNicxcdltgGwrc99ej3pzDPmmdqO+vR705hnzTO1GsjvDUz3M0jKeuo6d0WHNrhI12IHwy93hF14yC+4NwQRzE357rI9tbJM6Ogfigw8z0wDpdYJLlztbYu8PLlyX5htstv32aPencL+aZ2o77NHvTuF/NM7U9ZHeGpnuYYFOacmgqpZ3TmWd0AmzOc6JrxtzHeBmaASdvrVKsdW/bFRtrhMKiEUgjDtSYfAz5reD5y+bbutzK0dJ9GjM2U4zhJla0tD+Ux3ANiRe+7YPgpd9mjvp3C/mmdqWnHeGqnuZk0ehqI8OlkndUOqpJJSRO9xsA92WwOwC1t28WXmI24mYo5qZuIuxNuGyic1DX5Wzl0Vwy4tfY6wbs2Cy9H326O+ncL+aZ2o77dHfTuF/NM7U9NBqZ7mYNGxWh0mulnNOZhka9klx4BuCZfCy3sfbsXmu7p+GKH/AM63/wC29erdpdo61pJx3DDbhVMJ+q5L3VdL6bSGenosLcZKKmcXmWxAkeRbYDtsBf23WtiqsVTe3M9boXCVamLhJRdou7Z4JJMIXjI+kn1LB4iP3R9FNQg8RH7o+imujR8feZkXFNPvxdiHvN/Y1drXFNPvxdiHvN/Y1eF1g7vHzejPf6t95l5fVHnVF4upqJXJI7UrujBO5Q1Q4K0Qo2WVSFooraocEtUOCskJWVqQ9FFYxDgkYhwVkhIhWpD0UVTEOCRiHBWSErK0x6KK2qCRiCskJEK1IeiitqhwUdUOCtJEK0w0UVjEOCjqhwVqyiQrTHoorGMcEtUOCskJWVpj0SsYhwSMQ4KwQkQrTHoorGIcEjEOCs2SsqTHooraocEjEFZISsrTHoIr6sDmU2tsFOyNyu40rCUVJJUiz6lg8RF7o+iyLHB4iP3R9FNdGj488zIuKaffi7EPeb+xq7X0Limn34uxD3m/savC6wd3j5vRnQdW+8y8vqjzyvYJFTzYgG1oJpwx7nWNrWaTdUUrkblylOSjJNq52c4uUXFO1z002BQwUsUMtuWB72TOBJAPgEW223OHxVB2GQR4ph8bXvlgmn1Tw8WPgvyndzFajO7ynfFRzOuDc3BuNq2nWpt7IbjWjh6qVnU38/Y29dTUDoJpoXgPhaA5sGYxlxcQNr9u4C6zSYHG6siha+VpmlIDxHeNjdYWWve99n6getaEk7uYoL32tmda97X5+KpVYN3lAp4eqklGo/32m3bhsEOOU0GYzQvZrcrgQSbE5fXci3SrtJhFFJyGd7QYtWxk0ecgukeBlPTmJt/4CvNiR4kbIHu1gIIdfaCPWoF7uJ+KyQrQX5RTw9WVrVLbPc3cOBwvawyTTNGrD3uyCzrwuk8DbttlsfasgwihiMWd1RIx8ckmawFm8nEjdnEE8bGy8+ZH5QMzrDcL7ks77WzOt7VcatNL8APD129tTw3czdS4PEdVFTvkfLKHPiJG1/8Aaje1pHHwnDZz2VDEoIIoGGndmDZpYc/lhuUh1v8AqPQAq8FTNTytlheWyN+6618vrF9x9axySvfHGxxJZGCGjhc3KJTg07RsZadGrGS0p3S55/8ADEkU0ipRtnoaLC6SfCKWpdskjc+WcFxGeJp224G4A/6kVWAwgVMrTOAychsTGhxLNdqwG+v22vbpXnszrWubbrXRrZLWzvte/wB471sqpC1nE0nh62lpRqG4bhkFNj3JJnxzRMhfIXvJEbiIi4G7duW9t23YVmjw7Dp6iGSF5LZKhkGRgJjzFoLrEkOtvtzrzxcdm07BbfzJBzhuJFjfYVUakV+UuWHqS26x3tb/ALY3dLgkVRNTxmSoZni1r5HRjIf7Rks0852WWF1DFS4jXxD/ABDYaZ0jMzSLktG23qzX6FqtY/wRndZv3du72IbLIx5ex7mvIILgdpBFj8QVSnHwRWpq7bzvs5nqajBqForqhrQ2EQyMhizkkSsa7M7oyg2/+IFSnwKmgM7pZaoRwCTNeMAvLHxtu3b9059h9S0Jc7b4Tufn471EyPdve47LbTzcFl1kH+UiOFrRf/sdj07sGw+njqxMah5gilzSNt95k7GAgcbHceKxVGj0QrOSMmIliDnPcR95jZnsc71ENANuAK86ZH2IzusdpF96lHUzR6zI/bI0scSATY7wCdov6laqQf5RfZcQtqq7f9/0zYjHExtG+FhYJYM5BPOHubfpDQelUVkllfMWmRxdkaGNvzAbgsZUt3ew3qcXGNmJRUlFNGQ+poPER+6PopqEHiI/dH0U10aPj7zMi4pp9+LsQ95v7Grta4pp9+LsQ95v7GrwusHd4+b0Z7/VvvMvL6o88stKwOe4uAOVt7H2gf7rCrFH96T3P9wuUhmdlP8ACZg1vkM6oUS1vks6oV3DBEaomdodGGPJFuDSrXJY4qbVuaDMJQS+wPgkPsPZ4N+kLchScldGpOsoPRaNOWt8hnVCjZvkM6oWwq6WKOBzoy/MzV5sxFjmbfZ7LKUVDHJkH91pLY3FxtYhxAsPj+itUpXsPXxSuawtb5DOqEsrfIZ1Qti2nifFmGfI0vOXZmdYN57eu6GUcJhc9zpWElwaCDcWaDtsNu/1cVapsevijWlrfIZ1QlZvkM6oUikpRnI5W+QzqhItb5DOqFJRNlaGDWNcQ0sZY7Puha0raR/fb7QtWU5GWl4lyBjdSw5Wkm5JIvzqVm+QzqhKHxEfsP1K20NLDPh8F/AkDnPe8D/swbOPRs/VZoR0tiMFSoqbu95qS1vkM6oSyt8hnVC3NTRwzTzOa0xAtvGGmwBEYcdlvX6lRq6ZkNPDK0vJl22NvB2DYfXtv7LLI4NEwrxlZbylZvkM6gRZvkR9QLaMoIn6u5lb4suJtZ2Zt/B2cygKeF9O2W0hYGk5GkZj4Vt9legx69M1ha3yI+oEi1vkR9QLZGihFK6Rz5A8h7mgtOzKSLEW39Itda4p6NszJCankRs3yI+oEiG+RH1AmkU0ZBZW+RH1AsNWxuozhoBDgNgtvB7FnWKr/wAof+Y36OVlJ7UUEk1FJGyfU0HiI/dH0U1CDxEfuj6Ka6NHx95mRcU0+/F2I+839jV2vpXFNPvxdiHvN/Y1eF1g7vHzejPf6t95l5fVHnlnoz4bxzlmz4hYErkG4JB9S5OLs7naNXVi+CRuNju2IL3+U74qkZpfOv6xS10vnZOsVnU0Y9Wy45zjcFxINufgm+aR2UF5s21gDsFhZUXTS+dk6xUTNL52TrFWqgaouh72kFrnAg3BB3I1knhDO6zvvbd/tVLXS+dk6xUTNL52TrFWqg9UWykqhnl87J1ilr5fOydYqlJFatltRcqpnl87J1ikZ5fOydYq1ND1bLrL52+1asrI6WRws6R5HAuKxlNu5khHRzL0PiI/YfqVLO4CwcbWtvVFsj2CzHub7DZGvl89J1issZEum2y/rpQCBI8B28ZjtUHPc6+ZxN9puVSM83npOsUtfN52TrFWpgqRfknlflu91mgBovsFhZRbJIwgse5pG4gkWVAzzeek6xSM83npOsVamCo2Vi9rHhjmh7srjci+wqBVQzzeek6xUeUTeek6xVKRSptFspFVNfN56TrFLXzeek65VqSHq2XFiqzaltzl4t8D2hVuUTeek65UJHvebve5x9ZuquUqbvtIJJqKaMx9TQeIj90fRTUIPER+6Popro0fHnmZOhcU0+/F2Ie839jV2tcU0+/F2I+839jV4XWDu8fN6M6Dq33mXl9UeeQxj5HhsbHPcdwaLkoVzB5ooa3NUOa2N0cjLuDiLlpAvl22ueZcpTSlJJnZTk4xckrtFF7S1xa5pa4bwRYhKxtextuuvSS1+GPZVF8bZJi0ND3sJDgIw0BtwSLEX2kHde9ljmxChbTPbFqnE53RMNOLR3ADQdliRY7dvtW3qIK/30a0cVUdv/GzzrkivQOq8Mys8CMOcPAOpFof7dvC2eH4Vjz7vXZYpK6iaJBDHCbh/hGBu12rYGkAjYC4ONvWnqYpX00UsTNuypv/AH+/2Zo1F3sW/wARqcNkpKsQNYyR0l4gyIA7wdpI2C19xHCxC0LkpwUHZO5sUajqK7jYidySZ3JIRmEUimUiqQyKRTSKyIZPUymIyiJ5i8vKbfFYt4OzcvRUmI0keBNhfIBMIpoy0NcXnPusfu24328FOvr8NFLURURjBkhewu1IF/DY5oNmjbZrtw2Hn2XWyqUbX0jT+01FPR1bzz+W88y4FpIcCCNhBUV6GsxOknbWyGOEzvfIIzqGi7TIwg7t+XPtO1TlrMLdK8MEIc8SGKU0wywglha1zbeEQA8Xsfvc/Neqj4SGsVUsr03/AL9jzJQV6J+JYcJ5jBTwNhILmB9O1xDtdfnB/wCzvs3LBi0+HSYe5lIGNlE7iwMiAOTM/wC8SL7i3cfaBZVq0l+IqOJnJpOm1dmjO5RUuZRUI3AKSZSVooikfYmkVaASipKKtDPqaDxEfuj6KfQoQeIj90fRTXRo+PPMyLimn34uxD3m/sau1rimn34uxD3m/savC6wd3j5vRnQdW+8y8vqjzyA0vcGtFyULPR/ek9z/AHC5OKu7HZydlchyWXg3rjtUeSy8GddvarYSKzqCI05FU0svBvXb2qPJZeDeu3tVsqKtQQ9ORV5LLwZ129qRpZeDeu3tVopK1BBrJFU0sttzOu3tS5LLwZ129qtFJWooeskVTSy8Gddvaomll4M67e1W1EqlFD1kio6mla0ktBtwcD9FgK2jPvt9oWrKpqxlhJvMnHBJILtAtxJA+qkaSXgzrt7VYh8RH7D9SmsiiiXUdyqaSXgzrt7VHkkvBn5je1XCoq1FBrJFTkk3Bn5je1I0kvBn5je1WykrUUGskVeSS8GfmN7VHkk3Bn5je1WykVSih6yRU5JLwZ+Y3tS5JLwZ+Y3tVopFWooeskVOSTcGfmN7VimhfEfDFr84II/RbBYqv/KH/mN+jldkUpu+0oKKkkhGc+pYPER+6PopqEHiI/dH0U10aPjzzMi4pp9+LsQ95v7Grta4pp9+LsQ95v7GrwusHd4+b0Z0HVvvMvL6o88s9H96T3P9wsCzUZGd45y2w+IXKU8zsp/hNjhroY6tslS5wjjBeMouS4fd2e2yuzyUJmke3JK2clxc9pDmEs27AbDwvbzLUBRK3YVXGOjY1J0FOWld7i/i0dPFqBA0h8jdc8EWyZgPA6LE+wha5SKiiUtKV0rGSlBwiot3EUudMpJoyCKSZSVoYllkfBqvBjOtIANz4I9Y9f8A69mJRKuLsDVyUf327ecLVlbRn3hs2A3WrKJGal4l6HxEfsP1K29NJRswiSCWZ+uma6SwYMrXN+4Cb79jubc9aeHxEfsI/Upnes1OWia9WnrNl7Wdz0DGYdWVckLY44os8o1rA7wI25XNeeNwHjp9i0Ezg+aR7GhjXOJDRuaL7kiorLKel4CpUdX4t/URSTSSRmEUimUirRSIlThcxr/7rS5hFiAbEesKCRVoLXJzujc/+00tYBYXO0+sqvV/5Q/8xv0csqxVh/wtjvLwR0A9qoqKtZFBRUkkkbR9SweIj90fRT6Vjg8RH7o+iybF0aPjzzMi4pp9+LsQ95v7Grta4pp9+LsQ95v7GrwusHd4+b0Z0HVvvMvL6o88lcg3G9NShhlqJRFTxvlkO5jGlxPQFyUU27I7RtJXYjPN52TrFLXzX8bJ1iiWN8Ujo5WOY9psWuFiPaFj51lTaBKL2om6ebzsnWKRnm87J1ioOScCN4I59qpNj0Y7iZnm87J1ikZ5vOydYrNTUFXVRl9NS1EzGmxdHGXAHoCqOWT7ys2JOEm0rXRM1E3nZOsUtfN52TrFQO5JWmy9FbjIZ5vPSdYqJnm89J1ioFIqk2PRW4bppXts6R7hwLiVjKaRVopJLIbJHsvke5t+Bsmaibz0nXKTo3tY17mODHXyuI2G2+yxlWm0Fk9pkNRN56TrlLlE3npeuVAAuNmgk8Ah7HMNntLSQHWItsIuD8CsibHaN7D5RP56TrlBqJ/PSdcrEUiqTY9FbjLyibz0nXKjymfz0vXKgdyirTY9FbjLyifz0nXKjyifz0vXKgUlabHox3E+UT+el65WN73PN3uLjxJukkVaY0kshJJqKpFH1NB4iP3R9FPaoQeIj90fRT2Lo0fHnmZFxTT78XYh7zf2NXa1xTT78XYh7zf2NXhdYO7x83ozoOrfeZeX1R55WsMqY6SpdJNHrWGN7MhJAJLSBexBttVVDWl7g1u0rlIScZJo7OcVOLi8mbqLGozFE2eIC0rnOaxoIDdW1rLXNzlLb2O+yyuxOk5PmzEuJLZBqm3m/tgG+3wQTz+q+9aLk8nAfEJcnk4DrBbaxFTxNT7HR8HY3k2NUgjj5PAI3shLWERi7HZAN5Plbd3rUYcTjne+epcH6mnicxslheVgsBbnBuSf1WlNPJ6viFEwSer4hWsRUvtQ/sdG1kzNRVTYIK5j82aeHVttxztdt6Glbh1dAx0gEsUcjqfWvkY1ryJnOZcDmOxt9m65WgNPJwHxCRp5PV8QnTqzgrWLqYanUlpX+f8AXsberxWikw+phhpw10j3u2x77uBB2HYQPb+q0CzGnk4D4hLk8nAfEInOVR3aM1GnCimoswlI+xZjTycB1gkaeT1fEJJMzaS3mBIrMaeS2wA+whYSrSsUmnkbSOrpDSYcycPeaWQufHkBbI0uBte/AcFddjFITcGRs+wOqeTsJLcziWZb23Fo/wCm25aFkL3tzAC3EmyDTyf+HrBbEaslkjVnhqc3tf8Ar3/3M39JjdLS0lMyMSiWM+MDBdoMbmuttt95wPRtN1WjxSkbSujMbrmPK9uradb/AGWsFze4s4F3StQaeTgOsEuTycB1gsirTtYSwdG7d8/mbHHcThxCO0bCC2oe9hyNbaMhtm7PWCelaYrPyaTg3rBI00nBvWCUm5u7NmlCFGOhF7DAdyirBppODesFHk0nBvWCaTMukt5hKSzmnk4N6wUeTycG9YK0mPSjvMASPsWfk0nAdYLHLE+MAvGw897q0hqSZjUVJJUij6lg8RH7o+in0KEHiI9v+kfRT6V0aPjzzMi4pp9+LsQ95v7Grta4pp9+LsQ95v7GrwusHd4+b0Z0HVvvMvL6o88s9H96T3P9wsCz0f3pPc/3C5SnmdlP8JnCiVaw6SKGqEs+a0YLmhu8ut4P62PQrs1TRmWR4DH6+7n6xl3NJZtsbeVzhbsKaau3Y1J1ZRloqNzTlRWyxhsEZhbDGWSPbrZGkWLC4DwPYLEj3lrUSjoS0TJSnrIqVhFLnTKSaMgikmUlSGJRKksskkRisIrSkAE32e0DiskUDdjFH99vtC1ZW0ZfO32hasokZqXiXYfER+w/UqwKWU0+vswR7bEyNBNt9gTcrBD4iP2H6lX9fG7DWRZow9ua4cy527rHmWaCTzMFRyT+7vKIaXGzQSbE7PUoLcHEQZZHCV7byPyOF/Ba5pH1sVGKuiY2nBIysLCW5SS0jeRttt9nOsqhHeY9bPhNU1jnh5aLhgzO9QuB/uFBWqaoDXzPnu8vaBt25vCadvQCrklZGXH++8yHPlmy2MYNrDjzHduvsVKKfiW5yTtY1BUpInsBLxlINiCbHdfdvWyjq4WwztdK95fnBuCMxLbNNgbb+N1OXEI5Huc6R5c43DiCSw5C39CrUVvFrZ32RNKUitu3EGxvblkeTrIzI633wAQ6/tv0rUu3ptJGWEnK91YSxVf+UP8AzG/RyyrFV/5Q/wDMb9HJmVZooKKkkkjaPqSDxEfuj6LIoQeIj90fRT2ro0fHnmTXFdPvxdiHvN/Y1drXFNPvxdiHvM/Y1eF1g7vHzejOg6t95l5fVHnlmoz4bxzlth8QVhSK5OLs7naNXVi8kVSzu8o/FLO7yj8VmUyNWXSoqmXu8o/FRzu8o/FWpj1ZdKSp53eU74qLnu8p3xVqY9WXSPakqJe633j8UZ3eU74q1MerLqiQqZe7ynfFRL3eU74qlMerZsGCzgeYbStWVIucRYkke1RKpu5cI6Jbglj1TWueGluzaDt2+pSL4fPM+DuxUUisikDpq9y8ZIvPN+DuxLWReeZ8HdioFJWpBqlvLxkh88z4O7EGSHzzPg7sWvKCrUh6pby+ZIvPM+DuxIyQ+fZ8HdioHcoqlIeqW8vl8XnmfB3YkXxeeZ8HdiolJWpD1S3l7WQ+eZ8HdiwVczHRatjs13BxIHC/aqyRV6Q1TS2iUVJLeU0ZT6kg8RH7o+iyKEHiI9n+kfRTXRo+PPMyLmXdRwWRlW3FYWl0UgDJrf6SNgJ9RGzo9a6aoSxsmidHKxr43jK5rhcEcCtXG4WOLpOlL9vqbeBxcsHWVWO3f80fOyRXWMT7nuG1MjpKOaWkJN8oGdo6Dt/Va89zNnpV3y/8lykuhcXF2Ub/ALr1Oxp9PYOSvKTX1T9LnNSlzrpX9MmelXfL/wAkv6ZM9Ku+X/khdEYvg5r3MnbmB4+T9jmrkiulnuYs9Ku+X/kl/TFnpV3y/wDJUuicXwc17j7cwPHyfsc0Scumf0xZ6Wd8v/JI9zBh/wDezvl/5K10ViuHmvcO3cDx8n7HMjuSXTf6Xs9LO+X/AJI/pcz0s75f+SpdF4rh5r3H27gePk/Y5iUiunf0uZ6Wd8v/ACS/paz0s75f+SpdGYnh5r3H27gePk/Y5gkV1D+ljPS7vl/5Jf0sZ6Xd8v8AyVro3E8PNe4dvYHj5P2OXpFdR/pYz0u75f8AkonuVs9Lu+X/AJKl0diOHmvcfb2A/U5P2OXFJdS/pUz0u75b+SX9Kmel3fL/AMlS6PxHDzQ+3sB+pyfsctKRXU/6Us9Lu+X/AJJHuUM9MO+W/kqWAr8PNB2/gP1OT9jliiuq/wBKGemHfLfyS/pOz0w75b+StYGvw80Pt/Afqcn7HKykuq/0nZ6Yd8t/JL+k7PTDvlv5Klgq/DzQ+38B+pyfscpSK6t/SZnpl3y38kv6Ss9Mu+W/kqWDrbv6Dt/Afqcn7HKV6HQbR+XH8dhjyHkkThJO+2wNBvlvxO7/APi97RdyihjkDqzEaidg/wBMcYjv7Tcr3mE4XRYRRtpcOp2QQjbZu9x4k7yfWVsUcFK955HndIdZKOrcMLdyfjkl63LqEI2L1DiDIhCSBghCEACEIQAIQhAAhHSl0oAEIQgAQhCABCEIAEISQA0kdKOlAAhCECBCEIAEIQgYJIQgAQhHSgAQhHSgRNc60o0ux+fT0aJaFwYW6ugouXVdRiJk1bGlwDWAM23Nwb8CujLmGk+j2kWD90h+mGiVBTYqa2g5DV0U1SICHAgskDiCCPBaCN+z17GgZvu5vpl34aHHFH07aavp5JKarpwSWxzR7wDwILT02XO8K7qOl8Wi2B6WYzQ4FLgGI1YpXRUplZUx3kczMA4lp2sJsNvs2ke87lmh9ToloXNRV8kcuK1s0tbWOjN2a6TmbfmADR7QV5nuU9yDCcHwDB6nSbCmy6Q0r5JCJKl0sUbtY4tLWZsl8uU7t/rT2C2nsYu6JozJjtVhDcQPLqWSWOoaYXhsOraXPc91rNaADtJsea6ho53SNGNIq80eGV73VBidPG2Wnki10bd7mF7RmHsXmsJ0BxGSn7ptLXBlK3SKolNLO14cSxzHBpdbba53HmJVDA9F9KarFtF6jSHDqTDKTRbDpqZj4asTOrHuh1VwABkbYXsdvMiyHtPVYN3V9DsZrKCmocTe6Sufqqdz6WVjHyXtkzOaGhx4X4cVaxHuk6LYdjUuF1eIlk8MrYJpBBI6GKR1rMfKG5GnbznZz2XHO5bgekek/c80Hw6OhpIcCo8T+034iKn+4RHNIdWI7XDib+Fci1luK3uY47FimkGHMw84nhWL4m6uFQ/GZqeCNj3Nc5ssDD4bhbY4b9h5rJ2Qrs6XU90bRunx2rwY1c8mJUri2aCKllkLbMzk7GnZb4k23rxuBd15mkeiVJicZhwWqOKQUcrKinlqI3te51msc0NBcQ07dobz2uCvS6BaM4hgum+m+JVkLGUmKT0zqV4eHF7WRuBuN42nnXO8P7nellLoRQaMSYdTubhmPw18VWyqbaohzPLnZTYtI8HYdpv6krINp1DFO6TotheMy4ZWYkWTwyNhmkbBI6GF7rZWvkDS1p2852c9lLG+6Lo1guLz4biFbK2pp8nKDHSyyMgzgFmd7Wlrb3Frlc0xnuaY8cU0gw9mH/amE4xiJrhNJjE1PBE17muc2WnYfDcMuxw9XCyuaa6D6Vy6U19TorTCinqm07IcVo8UfT2DAGnlMJzCWwFhlA2b7lFkF2e07sOl1doZoYcXwmKlmqDURQtFQCWWed+wgow7H8awSixDE9PcR0bjwqnjB1mG60ua4mwzB1733ADaSqfdo0WxbSvufR4ThzI6qvFRA+TM4Rh4b947dnQqWm3cowyXRKopNC8Po8OrxUwVgZtEdQ6Euysf6rPd0kIVrD2noKPul6LVeE4piEdfIyHDGtkq45aeSOWJrvuu1ZaHEHiAVsZ9McEgqcMp5KpwlxGkfW0w1TvDiYzO527Z4O2x2rneIaD6SaYzaW4pjdLS4LU4lhTcMpKNlRr7ZXCTPI9ottcLbBcA+rbXodEtMq7FtGKvF8NoqWPC8IqcNcyKpEhLjDkbITs2PNvBF8trk7bAshXZ7bB+6loji+JYfQ0OKF1RXj/DZ6eRjZD5IcWgZvVe+0DevP8AdI7sOE4DgmM971VBWY1QSMh1ckEhgL9Y0PZnADS4NLjYOvs9RWqw/ufY7BoX3NcPdSQiswPFW1VaBK3+3HrHuJBvt3g2C1GkHc+01GhekmiGHYdh9VQ1eInEIK81QY97XSNdqywjY8WuXE2sCNtwnZBtO/wvL4Y3kC7mgn4KfQoQNcyCNrt7WgH4KagoEIQgQIQhAwQhCAEhNJAB0I6EIQAIQhAia19TjFDTVzKSaV4ncWjwYnua0uNmhzgMrSTsFyLrYrz2JYfXPxoVNFE2JxdEDOyoc0OYHeGJI7WcbXAO/bvG9MZuqOqhrads9M/PE4kB1iNoJB3+sFZl4oaK1MVKwU7KZszop21FnFonLp2SNa4gXILQ9t+bMlUaLVVS1zAIaWlqKhzZKeN5tFTkRktbYWuXxAkCwGsd0lkK57MytE7IiH5nNLgQ05bC287gdo2c+3gVIgFpBFwd4K8S/RnEqmgg5a+nkrZA99WQ45XOdPA6w2bgyIjoCw4/hJw8vLaSCajdygQUuV+VjntjIc0NaQHBzX2Gw+GbHeiwXPWySYdgNDFGyJlNT5skcNNATdxu4hrGC53E7BxKt008VTTxz07xJDI0PY9u5wO4rSxUdZHhOBOhhY+oo2M1kMj8l/7RYdtjtBP1WJ2CVLdHaCiOqnfBK2WeEm0cwuS5m0bgTcXG3KL2QBup62GGupqR2sM84c5gawkBrbXJI2AbQNvOViditE3EhQGU8pJy2yOy5subKXWyh2Xblve22y1WEYC6nxGhrJ6ala+CGoYMvhGPPKHMY023NbmbzW5thUjhVYMTma1sRo5q5la6UyEPblY0ZMttvhMG2+4nhtA2m8ZPE+okgZI0zRhrnsB2tDr2J9tj8EzNGKgQF7dcWl4ZfaWggE/Ej4rzGLaPzT41W1kVNTTR1ApS5r3ZTII3uzMds3EFpHMS0A7FUZozWRAytp6J07qeqhaHHMIWyS52MBI2tDbt4DhZFguewfVQsq4qZ77Tytc9jbHaG2zG+7/UPisy8no3gNbQYjHNPkEEbqjI27bta8Q2FmNa0bWPJAHPz71r5tH62vjrDBTw0z3zV15y8h87XiVjWOFtjczg7eR4LSNp2Fgue8QvK1uj8prm8ipqWKMOp3RTtOV1O1j8z2taBudt3HbmN9i1UGj2IVeHv5PHHh8jop2PkY7w6jNK1zQ67dgytI23tm2AhFgue/WCnqYaixhcXsdG2Vrw05XNdexDtx3fTiF5XCdFjFiNNLWwxy0sbaj+1Nq3ZXPMOWzWsa0eLedg3m9zdV3aLVv2VTUsIip2Qw0sT4oi20uqdLn2OaW7c7XC4NyNtt6LAe4QvG0ejtbTT4XI2NjnU+UOdNIyTK3WucQP7YykNdsyWG4EENC9khgCEISAEIQgYJJoQAkIQgAQhCBAhCEATQhCBghCEACEIQAI6UJIAEIQgAQhCABCEIAEIQgAS6U0kACEIQIEIQgAQhCBgkmhACR0oQgA6UIQgQISTQBNCEIGCEIQAIQhAAjahLYgBpIQgAQhCABCEIAEIQgARtQlsQAIQhAAhCECBCEIGCEJIAEbUIQAIRsQgQIQhAE0IQgYIQhAAhCEAHQjoR0o6UAJCEIAEIQgAQhCABCEIAEuhNLpQAdCEdKECBCEIAEIQgYJJpIAEdCEdKADoQjpQgQIQhAE0IQgYIQhAAkhCABCEIAEIQgAQhCABCEIAEkIQAIQhAgQhCABCEIGCEIQAkcyEIAEkIQIEIQgAQhCAP/Z"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 2507, "timestamp": 227738092177, "data": "data:image/jpeg;base64,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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 0.95, "scoreDisplayMode": "numeric", "numericValue": 140, "numericUnit": "millisecond", "displayValue": "140 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 0.84, "scoreDisplayMode": "numeric", "numericValue": 149, "numericUnit": "millisecond", "displayValue": "150 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0.00248979117532291, "numericUnit": "unitless", "displayValue": "0.002", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0.00248979117532291, "newEngineResult": {"cumulativeLayoutShift": 0.00248979117532291, "cumulativeLayoutShiftMainFrame": 0.00248979117532291}, "newEngineResultDiffered": false}]}}, "errors-in-console": {"id": "errors-in-console", "title": "Browser errors were logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "sourceLocation", "valueType": "source-location", "label": "Source"}, {"key": "description", "valueType": "code", "label": "Description"}], "items": [{"source": "console.error", "description": "Failed to initialize real-time connection: ReferenceError: supabase is not defined\n    at hm.initializeConnection (http://localhost:4173/assets/index-CGQEF-7R.js:786:51331)\n    at http://localhost:4173/assets/index-CGQEF-7R.js:786:51881", "sourceLocation": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 785, "column": 51523}}, {"source": "console.error", "description": "Failed to initialize real-time connection: ReferenceError: supabase is not defined\n    at hm.initializeConnection (http://localhost:4173/assets/index-CGQEF-7R.js:786:51331)\n    at http://localhost:4173/assets/index-CGQEF-7R.js:786:51881", "sourceLocation": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 785, "column": 51523}}, {"source": "console.error", "description": "Failed to initialize real-time connection: ReferenceError: supabase is not defined\n    at hm.initializeConnection (http://localhost:4173/assets/index-CGQEF-7R.js:786:51331)\n    at new hm (http://localhost:4173/assets/index-CGQEF-7R.js:786:51169)\n    at hm.getInstance (http://localhost:4173/assets/index-CGQEF-7R.js:786:51246)\n    at http://localhost:4173/assets/index-CGQEF-7R.js:786:55239", "sourceLocation": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 785, "column": 51523}}, {"source": "network", "description": "Failed to load resource: net::ERR_NAME_NOT_RESOLVED", "sourceLocation": {"type": "source-location", "url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "urlProvider": "network", "line": 0, "column": 0}}, {"source": "network", "description": "Failed to load resource: net::ERR_NAME_NOT_RESOLVED", "sourceLocation": {"type": "source-location", "url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "urlProvider": "network", "line": 0, "column": 0}}]}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0.7949999999999999, "numericUnit": "millisecond", "displayValue": "Root document took 0 ms", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:4173/properties", "responseTime": 0.7949999999999999}], "overallSavingsMs": 0}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.45, "scoreDisplayMode": "numeric", "numericValue": 7776.0301, "numericUnit": "millisecond", "displayValue": "7.8 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "4 chains found", "details": {"type": "criticalrequestchain", "chains": {"FB06D34F4C78277F678CCC389C67ACEA": {"request": {"url": "http://localhost:4173/properties", "startTime": 227735.586329, "endTime": 227735.587546, "responseReceivedTime": 227735.58739300002, "transferSize": 1125}, "children": {"44940.2": {"request": {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "startTime": 227735.592393, "endTime": 227735.653653, "responseReceivedTime": 227735.596844, "transferSize": 805489}}, "44940.3": {"request": {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "startTime": 227735.592584, "endTime": 227735.598332, "responseReceivedTime": 227735.596223, "transferSize": 21531}, "children": {"44940.8": {"request": {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "startTime": 227735.599264, "endTime": 227736.129192, "responseReceivedTime": 227736.12777899997, "transferSize": 1312}, "children": {"44940.16": {"request": {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "startTime": 227736.301263, "endTime": 227736.815296, "responseReceivedTime": 227736.619115, "transferSize": 48981}}}}, "44940.9": {"request": {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "startTime": 227735.599657, "endTime": 227736.129694, "responseReceivedTime": 227736.129439, "transferSize": 737}}}}, "44940.4": {"request": {"url": "https://cdn.gpteng.co/gptengineer.js", "startTime": 227735.592723, "endTime": 227736.129999, "responseReceivedTime": 227736.129878, "transferSize": 232}}}}}, "longestChain": {"duration": 1228.9669999480247, "length": 4, "transferSize": 48981}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 707.2559999999971, "numericUnit": "millisecond", "displayValue": "0.7 s", "metricSavings": {"TBT": 150}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 295.32399999999706}, {"group": "other", "groupLabel": "Other", "duration": 281.7120000000001}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 90.28799999999998}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 21.471999999999987}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 11.791999999999998}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 6.668}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 302.4119999999996, "numericUnit": "millisecond", "displayValue": "0.3 s", "metricSavings": {"TBT": 150}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "Unattributable", "total": 410.3759999999985, "scripting": 158.6519999999985, "scriptParseCompile": 0}, {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "total": 143.68400000000105, "scripting": 125.26400000000105, "scriptParseCompile": 11.068}, {"url": "http://localhost:4173/properties", "total": 138.06399999999996, "scripting": 7.091999999999998, "scriptParseCompile": 0.336}], "summary": {"wastedMs": 302.4119999999996}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 632.5051, "numericUnit": "millisecond", "displayValue": "Est savings of 630 ms", "warnings": [], "metricSavings": {"LCP": 650, "FCP": 650}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://fonts.gstatic.com", "wastedMs": 632.5051}], "overallSavingsMs": 632.5051, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 12, "numScripts": 2, "numStylesheets": 3, "numFonts": 1, "numTasks": 367, "numTasksOver10ms": 4, "numTasksOver25ms": 2, "numTasksOver50ms": 1, "numTasksOver100ms": 0, "numTasksOver500ms": 0, "rtt": 0.02445, "throughput": 27953700.16930623, "maxRtt": 166.277, "maxServerLatency": 90.26499999999999, "totalByteWeight": 894763, "totalTaskTime": 176.81400000000005, "mainDocumentTransferSize": 1125}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "http://localhost:4173/properties", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 0, "networkRequestTime": 0.5480000376701355, "networkEndTime": 1.7650000154972076, "finished": true, "transferSize": 1125, "resourceSize": 875, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.165000021457672, "networkRequestTime": 6.612000018358231, "networkEndTime": 67.87200000882149, "finished": true, "transferSize": 805489, "resourceSize": 2958309, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.312999993562698, "networkRequestTime": 6.8030000030994415, "networkEndTime": 12.550999999046326, "finished": true, "transferSize": 21531, "resourceSize": 144473, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "https://cdn.gpteng.co/gptengineer.js", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 6.3730000257492065, "networkRequestTime": 6.942000031471252, "networkEndTime": 544.2179999947548, "finished": true, "transferSize": 232, "resourceSize": 0, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "gpteng.co"}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 12.96900001168251, "networkRequestTime": 13.483000010251999, "networkEndTime": 543.4110000133514, "finished": true, "transferSize": 1312, "resourceSize": 15042, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 13.03999999165535, "networkRequestTime": 13.87600001692772, "networkEndTime": 543.9130000174046, "finished": true, "transferSize": 737, "resourceSize": 6666, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "sessionTargetType": "page", "protocol": "", "rendererStartTime": 601.6910000145435, "networkRequestTime": 601.6910000145435, "networkEndTime": 864.8570000231266, "finished": true, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "phcityrent.com"}, {"url": "http://localhost:4173/favicon.ico", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 607.8400000333786, "networkRequestTime": 608.21099999547, "networkEndTime": 609.8709999918938, "finished": true, "transferSize": 15356, "resourceSize": 15086, "statusCode": 200, "mimeType": "image/x-icon", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "sessionTargetType": "page", "protocol": "", "rendererStartTime": 636.1450000107288, "networkRequestTime": 636.1450000107288, "networkEndTime": 864.0240000188351, "finished": true, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "Preflight", "priority": "High", "entity": "phcityrent.com"}, {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 704.227000027895, "networkRequestTime": 715.4820000231266, "networkEndTime": 1229.5149999856949, "finished": true, "transferSize": 48981, "resourceSize": 48432, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "sessionTargetType": "page", "protocol": "", "rendererStartTime": 1868.1389999985695, "networkRequestTime": 1868.1389999985695, "networkEndTime": 1904.6030000150204, "finished": true, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "phcityrent.com"}, {"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "sessionTargetType": "page", "protocol": "", "rendererStartTime": 1868.7930000126362, "networkRequestTime": 1868.7930000126362, "networkEndTime": 1904.4570000171661, "finished": true, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "Preflight", "priority": "High", "entity": "phcityrent.com"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 227735585781}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 166.277, "numericUnit": "millisecond", "displayValue": "170 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://fonts.gstatic.com", "rtt": 166.277}, {"origin": "https://cdn.gpteng.co", "rtt": 137.937}, {"origin": "https://fonts.googleapis.com", "rtt": 133.91400000000002}, {"origin": "http://localhost:4173", "rtt": 0.02445}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 90.26499999999999, "numericUnit": "millisecond", "displayValue": "90 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://cdn.gpteng.co", "serverResponseTime": 90.26499999999999}, {"origin": "https://fonts.googleapis.com", "serverResponseTime": 90.14749999999998}, {"origin": "http://localhost:4173", "serverResponseTime": 2.01505}, {"origin": "https://fonts.gstatic.com", "serverResponseTime": 0}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 50.703, "startTime": 85.211}, {"duration": 37.131, "startTime": 545.713}, {"duration": 22.758, "startTime": 584.988}, {"duration": 13.548, "startTime": 702.209}, {"duration": 6.242, "startTime": 1906.15}, {"duration": 9.211, "startTime": 2012.456}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 7776, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 6905, "largestContentfulPaint": 7776, "interactive": 7776, "speedIndex": 6905, "totalBlockingTime": 140, "maxPotentialFID": 149, "cumulativeLayoutShift": 0.00248979117532291, "cumulativeLayoutShiftMainFrame": 0.00248979117532291, "timeToFirstByte": 452, "observedTimeOrigin": 0, "observedTimeOriginTs": 227735585187, "observedNavigationStart": 0, "observedNavigationStartTs": 227735585187, "observedFirstPaint": 2303, "observedFirstPaintTs": 227737888508, "observedFirstContentfulPaint": 2303, "observedFirstContentfulPaintTs": 227737888508, "observedFirstContentfulPaintAllFrames": 2303, "observedFirstContentfulPaintAllFramesTs": 227737888508, "observedLargestContentfulPaint": 2303, "observedLargestContentfulPaintTs": 227737888508, "observedLargestContentfulPaintAllFrames": 2303, "observedLargestContentfulPaintAllFramesTs": 227737888508, "observedTraceEnd": 4621, "observedTraceEndTs": 227740205989, "observedLoad": 585, "observedLoadTs": 227736169843, "observedDomContentLoaded": 583, "observedDomContentLoadedTs": 227736168173, "observedCumulativeLayoutShift": 0.00248979117532291, "observedCumulativeLayoutShiftMainFrame": 0.00248979117532291, "observedFirstVisualChange": 2295, "observedFirstVisualChangeTs": 227737880187, "observedLastVisualChange": 2506, "observedLastVisualChangeTs": 227738091187, "observedSpeedIndex": 2298, "observedSpeedIndexTs": 227737883358}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 11, "transferSize": 879407}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 2, "transferSize": 805721}, {"resourceType": "font", "label": "Font", "requestCount": 1, "transferSize": 48981}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 3, "transferSize": 23580}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 1125}, {"resourceType": "image", "label": "Image", "requestCount": 0, "transferSize": 0}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "other", "label": "Other", "requestCount": 4, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 8, "transferSize": 51262}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "Third-party code blocked the main thread for 0 ms", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "Third-Party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer Size", "subItemsHeading": {"key": "transferSize"}}, {"key": "blockingTime", "granularity": 1, "valueType": "ms", "label": "Main-Thread Blocking Time", "subItemsHeading": {"key": "blockingTime"}}], "items": [{"mainThreadTime": 3.62, "blockingTime": 0, "transferSize": 51030, "tbtImpact": 0, "entity": "Google Fonts", "subItems": {"type": "subitems", "items": [{"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 48981, "tbtImpact": 0}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "mainThreadTime": 1.484, "blockingTime": 0, "transferSize": 1312, "tbtImpact": 0}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "mainThreadTime": 2.136, "blockingTime": 0, "transferSize": 737, "tbtImpact": 0}]}}, {"mainThreadTime": 1.6960000000000002, "blockingTime": 0, "transferSize": 232, "tbtImpact": 0, "entity": "gpteng.co", "subItems": {"type": "subitems", "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "mainThreadTime": 1.6960000000000002, "blockingTime": 0, "transferSize": 232, "tbtImpact": 0}]}}, {"mainThreadTime": 0, "blockingTime": 0, "transferSize": 0, "tbtImpact": 0, "entity": "phcityrent.com", "subItems": {"type": "subitems", "items": [{"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 0, "tbtImpact": 0}]}}], "summary": {"wastedBytes": 51262, "wastedMs": 0}, "isEntityGrouped": true}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "7,780 ms", "metricSavings": {"LCP": 5300}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-H1", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,0,DIV,0,H1", "selector": "div.container > div.space-y-8 > div.bg-gradient-to-r > h1.text-3xl", "boundingRect": {"top": 206, "bottom": 278, "left": 48, "right": 364, "width": 316, "height": 72}, "snippet": "<h1 class=\"text-3xl font-bold mb-6\">", "nodeLabel": "Find Your Perfect Home"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 452.01505, "percent": "6%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 7324.01505, "percent": "94%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "1 layout shift found", "metricSavings": {"CLS": 0.002}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "extra"}, "label": "Element"}, {"key": "score", "valueType": "numeric", "subItemsHeading": {"key": "cause", "valueType": "text"}, "granularity": 0.001, "label": "Layout shift score"}], "items": [{"node": {"type": "node", "lhId": "page-1-DIV", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,2,DIV", "selector": "main.pt-20 > div.container > div.space-y-8 > div.text-center", "boundingRect": {"top": 774, "bottom": 1134, "left": 16, "right": 396, "width": 380, "height": 360}, "snippet": "<div class=\"text-center py-16\">", "nodeLabel": "No Properties Found\n\nTry adjusting your search criteria to find more properties…"}, "score": 0.00248979117532291}]}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "3 long tasks found", "metricSavings": {"TBT": 150}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Duration"}], "items": [{"url": "Unattributable", "duration": 203, "startTime": 603.01505}, {"url": "Unattributable", "duration": 149, "startTime": 7204.0301}, {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "duration": 91, "startTime": 7354.0301}], "sortedBy": ["duration"], "skipSumming": ["startTime"], "debugData": {"type": "debugdata", "urls": ["Unattributable", "http://localhost:4173/assets/index-CGQEF-7R.js"], "tasks": [{"urlIndex": 0, "startTime": 603, "duration": 203, "other": 203}, {"urlIndex": 0, "startTime": 7204, "duration": 149, "other": 149, "scriptEvaluation": 0}, {"urlIndex": 1, "startTime": 7354, "duration": 91, "other": 91}]}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Missing source maps for large first-party JavaScript", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "scriptUrl", "valueType": "url", "subItemsHeading": {"key": "error"}, "label": "URL"}, {"key": "sourceMapUrl", "valueType": "url", "label": "Map URL"}], "items": [{"scriptUrl": "http://localhost:4173/assets/index-CGQEF-7R.js", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}]}}]}}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No CSP found in enforcement mode"}]}}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No HSTS header found"}]}}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"description": "No COOP header found", "severity": "High"}]}}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No frame control policy found"}]}}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "http://localhost:4173/assets/index-CGQEF-7R.js", "resourceBytes": 2956446, "encodedBytes": 805133, "unusedBytes": 2308269}, {"name": "https://cdn.gpteng.co/gptengineer.js", "resourceBytes": 0, "encodedBytes": 0}]}}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "button-name": {"id": "button-name", "title": "Buttons do not have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-BUTTON", "path": "1,HTML,1,BODY,0,DIV,2,DIV,0,NAV,0,DIV,0,DIV,3,DIV,0,BUTTON", "selector": "div.max-w-7xl > div.flex > div.md:hidden > button.text-gray-700", "boundingRect": {"top": 10, "bottom": 54, "left": 356, "right": 396, "width": 40, "height": 44}, "snippet": "<button class=\"text-gray-700 hover:text-orange-600 p-2 hover:scale-110 transition-all dur…\">", "nodeLabel": "div.max-w-7xl > div.flex > div.md:hidden > button.text-gray-700", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}, {"node": {"type": "node", "lhId": "1-1-BUTTON", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,0,DIV,2,DIV,2,DIV,1,BUTTON", "selector": "div.bg-gradient-to-r > div.grid > div.space-y-2 > button.flex", "boundingRect": {"top": 486, "bottom": 530, "left": 48, "right": 198, "width": 150, "height": 44}, "snippet": "<button type=\"button\" role=\"combobox\" aria-controls=\"radix-:r0:\" aria-expanded=\"false\" aria-autocomplete=\"none\" dir=\"ltr\" data-state=\"closed\" data-placeholder=\"\" class=\"flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 …\">", "nodeLabel": "Any", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}, {"node": {"type": "node", "lhId": "1-2-BUTTON", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,0,DIV,2,DIV,3,DIV,1,BUTTON", "selector": "div.bg-gradient-to-r > div.grid > div.space-y-2 > button.flex", "boundingRect": {"top": 486, "bottom": 530, "left": 214, "right": 364, "width": 150, "height": 44}, "snippet": "<button type=\"button\" role=\"combobox\" aria-controls=\"radix-:r1:\" aria-expanded=\"false\" aria-autocomplete=\"none\" dir=\"ltr\" data-state=\"closed\" data-placeholder=\"\" class=\"flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 …\">", "nodeLabel": "Any", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}, {"node": {"type": "node", "lhId": "1-3-BUTTON", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,0,DIV,2,DIV,4,DIV,1,BUTTON", "selector": "div.bg-gradient-to-r > div.grid > div.space-y-2 > button.flex", "boundingRect": {"top": 578, "bottom": 622, "left": 48, "right": 198, "width": 150, "height": 44}, "snippet": "<button type=\"button\" role=\"combobox\" aria-controls=\"radix-:r2:\" aria-expanded=\"false\" aria-autocomplete=\"none\" dir=\"ltr\" data-state=\"closed\" data-placeholder=\"\" class=\"flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 …\">", "nodeLabel": "Any", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}], "debugData": {"type": "debugdata", "impact": "critical", "tags": ["cat.name-role-value", "wcag2a", "wcag412", "section508", "section508.22.a", "TTv5", "TT6.a", "EN-301-549", "EN-*******", "ACT"]}}}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "notApplicable"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors have a sufficient contrast ratio", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "notApplicable"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "document-title": {"id": "document-title", "title": "Document has a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "notApplicable"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "notApplicable"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "notApplicable"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "notApplicable"}, "heading-order": {"id": "heading-order", "title": "Heading elements appear in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element has a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "notApplicable"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "notApplicable"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "target-size": {"id": "target-size", "title": "Touch targets have sufficient size and spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "notApplicable"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "notApplicable"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "manual"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "manual"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "manual"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "manual"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "manual"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "manual"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "manual"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "manual"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "manual"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "manual"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Serve static assets with an efficient cache policy", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 157.76, "numericUnit": "byte", "displayValue": "1 resource found", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "debugData": {"type": "debugdata", "public": true, "max-age": 14400}, "cacheLifetimeMs": 14400000, "cacheHitProbability": 0.32, "totalBytes": 232, "wastedBytes": 157.76}], "summary": {"wastedBytes": 157.76}, "sortedBy": ["totalBytes"], "skipSumming": ["cacheLifetimeMs"]}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 894763, "numericUnit": "byte", "displayValue": "Total size was 874 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "totalBytes": 805489}, {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "totalBytes": 48981}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "totalBytes": 21531}, {"url": "http://localhost:4173/favicon.ico", "totalBytes": 15356}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "totalBytes": 1312}, {"url": "http://localhost:4173/properties", "totalBytes": 1125}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "totalBytes": 737}, {"url": "https://cdn.gpteng.co/gptengineer.js", "totalBytes": 232}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1500, "numericUnit": "millisecond", "displayValue": "Est savings of 1,500 ms", "metricSavings": {"FCP": 1500, "LCP": 1500}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "totalBytes": 737, "wastedMs": 284}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "totalBytes": 1312, "wastedMs": 1242}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "totalBytes": 21531, "wastedMs": 452}], "overallSavingsMs": 1500}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 300, "numericUnit": "millisecond", "displayValue": "Est savings of 19 KiB", "metricSavings": {"FCP": 150, "LCP": 300}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "wastedBytes": 19252, "wastedPercent": 90.88272549196044, "totalBytes": 21183}], "overallSavingsMs": 300, "overallSavingsBytes": 19252, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 150, "LCP": 300}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 3270, "numericUnit": "millisecond", "displayValue": "Est savings of 613 KiB", "metricSavings": {"FCP": 2700, "LCP": 3250}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Est Savings"}], "items": [{"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "totalBytes": 804626, "wastedBytes": 628218, "wastedPercent": 78.075804530169}], "overallSavingsMs": 3270, "overallSavingsBytes": 628218, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 2700, "LCP": 3270}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 1 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "wastedBytes": 586, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 801, "column": 38571}}, {"signal": "@babel/plugin-transform-spread", "location": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 812, "column": 65526}}]}, "totalBytes": 0}], "overallSavingsMs": 0, "overallSavingsBytes": 586, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": 1, "scoreDisplayMode": "binary"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": 1, "scoreDisplayMode": "binary"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 210, "numericUnit": "element", "displayValue": "210 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 210}}, {"node": {"type": "node", "lhId": "1-31-path", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,0,NAV,0,DIV,0,DIV,0,A,0,DIV,0,svg,0,path", "selector": "a.flex > div.p-1.5 > svg.lucide > path", "boundingRect": {"top": 111, "bottom": 116, "left": 44, "right": 48, "width": 4, "height": 5}, "snippet": "<path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\">", "nodeLabel": "a.flex > div.p-1.5 > svg.lucide > path"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 12}}, {"node": {"type": "node", "lhId": "1-32-svg", "path": "1,HTML,1,BODY,0,DIV,2,DIV,0,NAV,0,DIV,0,DIV,1,DIV,0,A,0,svg", "selector": "div.flex > div.hidden > a.px-3 > svg.lucide", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-building w-4 h-4\">", "nodeLabel": "div.flex > div.hidden > a.px-3 > svg.lucide"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 11}}]}, "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "notApplicable"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document has a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": 1, "scoreDisplayMode": "binary"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": 1, "scoreDisplayMode": "binary"}, "font-size": {"id": "font-size", "title": "Document uses legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": 1, "scoreDisplayMode": "binary", "displayValue": "100% legible text", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "selector", "valueType": "code", "label": "Selector"}, {"key": "coverage", "valueType": "text", "label": "% of Page Text"}, {"key": "fontSize", "valueType": "text", "label": "Font Size"}], "items": [{"source": {"type": "code", "value": "Legible text"}, "selector": "", "coverage": "100.00%", "fontSize": "≥ 12px"}]}}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": 1, "scoreDisplayMode": "binary", "warnings": [], "details": {"type": "table", "headings": [], "items": []}}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is not valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": 0, "scoreDisplayMode": "binary", "displayValue": "18 errors found", "details": {"type": "table", "headings": [{"key": "index", "valueType": "text", "label": "Line #"}, {"key": "line", "valueType": "code", "label": "Content"}, {"key": "message", "valueType": "code", "label": "Error"}], "items": [{"index": "2", "line": "<!DOCTYPE html>", "message": "Syntax not understood"}, {"index": "3", "line": "<html lang=\"en\">", "message": "Syntax not understood"}, {"index": "4", "line": "  <head>", "message": "Syntax not understood"}, {"index": "5", "line": "    <meta charset=\"UTF-8\" />", "message": "Syntax not understood"}, {"index": "6", "line": "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />", "message": "Syntax not understood"}, {"index": "7", "line": "    <title>PHCityRent - Port Harcourt's Trusted Rental Platform</title>", "message": "Syntax not understood"}, {"index": "8", "line": "    <meta name=\"description\" content=\"Find verified properties and agents in Port Harcourt. Secure rent payments, avoid scams, and rent safely with PHCityRent.\" />", "message": "Syntax not understood"}, {"index": "9", "line": "    <meta name=\"author\" content=\"PHCityRent Team\" />", "message": "Syntax not understood"}, {"index": "10", "line": "    <meta property=\"og:image\" content=\"/og-image-update.png\" />", "message": "Unknown directive"}, {"index": "11", "line": "    <script type=\"module\" crossorigin src=\"/assets/index-CGQEF-7R.js\"></script>", "message": "Syntax not understood"}, {"index": "12", "line": "    <link rel=\"stylesheet\" crossorigin href=\"/assets/index-AM9p-ZF3.css\">", "message": "Syntax not understood"}, {"index": "13", "line": "  </head>", "message": "Syntax not understood"}, {"index": "15", "line": "  <body>", "message": "Syntax not understood"}, {"index": "16", "line": "    <div id=\"root\"></div>", "message": "Syntax not understood"}, {"index": "17", "line": "    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->", "message": "Unknown directive"}, {"index": "18", "line": "    <script src=\"https://cdn.gpteng.co/gptengineer.js\" type=\"module\"></script>", "message": "Unknown directive"}, {"index": "19", "line": "  </body>", "message": "Syntax not understood"}, {"index": "20", "line": "</html>", "message": "Syntax not understood"}]}}, "hreflang": {"id": "hreflang", "title": "Document has a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "canonical": {"id": "canonical", "title": "Document has a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": null, "scoreDisplayMode": "notApplicable"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "manual"}, "bf-cache": {"id": "bf-cache", "title": "Page didn't prevent back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 1, "scoreDisplayMode": "binary", "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "displayValue": "Est savings of 0 KiB", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "Request"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "cacheLifetimeMs": 14400000, "wastedBytes": 157.76}], "sortedBy": ["wastedBytes"], "skipSumming": ["cacheLifetimeMs"], "debugData": {"type": "debugdata", "wastedBytes": 157.76}}, "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "extra"}, "label": "Element"}, {"key": "score", "valueType": "numeric", "subItemsHeading": {"key": "cause", "valueType": "text"}, "granularity": 0.001, "label": "Layout shift score"}], "items": [{"node": {"type": "text", "value": "Total"}, "score": 0.00248979117532291}, {"node": {"type": "node", "lhId": "page-1-DIV", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,2,DIV", "selector": "main.pt-20 > div.container > div.space-y-8 > div.text-center", "boundingRect": {"top": 774, "bottom": 1134, "left": 16, "right": 396, "width": 380, "height": 360}, "snippet": "<div class=\"text-center py-16\">", "nodeLabel": "No Properties Found\n\nTry adjusting your search criteria to find more properties…"}, "score": 0.00248979117532291}]}]}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Avoids redirects", "value": true}, "serverResponseIsFast": {"label": "Server responds quickly (observed 1 ms) ", "value": true}, "usesCompression": {"label": "Applies text compression", "value": true}}, "debugData": {"type": "debugdata", "redirectDuration": 0, "serverResponseTime": 1, "uncompressedResponseBytes": 0, "wastedBytes": 0}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 248}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-3-svg", "path": "1,HTML,1,BODY,0,DIV,2,DIV,0,NAV,0,DIV,0,DIV,1,DIV,0,A,0,svg", "selector": "div.flex > div.hidden > a.px-3 > svg.lucide", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-building w-4 h-4\">", "nodeLabel": "div.flex > div.hidden > a.px-3 > svg.lucide"}, "value": {"type": "numeric", "granularity": 1, "value": 11}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-4-path", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,0,NAV,0,DIV,0,DIV,0,A,0,DIV,0,svg,0,path", "selector": "a.flex > div.p-1.5 > svg.lucide > path", "boundingRect": {"top": 111, "bottom": 116, "left": 44, "right": 48, "width": 4, "height": 5}, "snippet": "<path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\">", "nodeLabel": "a.flex > div.p-1.5 > svg.lucide > path"}, "value": {"type": "numeric", "granularity": 1, "value": 12}}], "debugData": {"type": "debugdata", "totalElements": 248, "maxChildren": 11, "maxDepth": 12}}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.", "score": 1, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [], "items": []}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "INP by phase", "description": "Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "LCP by phase", "description": "Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Phase"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"phase": "timeToFirstByte", "label": "Time to first byte", "duration": 2.1530000305175783}, {"phase": "elementRenderDelay", "label": "Element render delay", "duration": 2301.1679999694825}]}, {"type": "node", "lhId": "page-0-H1", "path": "1,HTML,1,BODY,0,DIV,2,DIV,1,MAIN,0,DIV,1,DIV,0,DIV,0,H1", "selector": "div.container > div.space-y-8 > div.bg-gradient-to-r > h1.text-3xl", "boundingRect": {"top": 206, "bottom": 278, "left": 48, "right": 364, "width": 316, "height": 72}, "snippet": "<h1 class=\"text-3xl font-bold mb-6\">", "nodeLabel": "Find Your Perfect Home"}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable older browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support older browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 0, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "network-tree", "chains": {"FB06D34F4C78277F678CCC389C67ACEA": {"url": "http://localhost:4173/properties", "navStartToEndTime": 7, "transferSize": 1125, "isLongest": true, "children": {"44940.2": {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "navStartToEndTime": 84, "transferSize": 805489, "isLongest": true, "children": {"44940.72": {"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "navStartToEndTime": 1905, "transferSize": 0, "isLongest": true, "children": {}}, "44940.70": {"url": "https://api.phcityrent.com/api/v1/properties?is_available=true", "navStartToEndTime": 865, "transferSize": 0, "children": {}}}}, "44940.3": {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "navStartToEndTime": 14, "transferSize": 21531, "children": {"44940.8": {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "navStartToEndTime": 544, "transferSize": 1312, "children": {"44940.16": {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "navStartToEndTime": 1231, "transferSize": 48981, "children": {}}}}, "44940.9": {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "navStartToEndTime": 544, "transferSize": 737, "children": {}}}}, "44940.4": {"url": "https://cdn.gpteng.co/gptengineer.js", "navStartToEndTime": 545, "transferSize": 232, "children": {}}}}}, "longestChain": {"duration": 1905}}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) can move these network requests out of the critical path.", "score": 0, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 1500, "LCP": 1500}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "totalBytes": 737, "wastedMs": 284}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "totalBytes": 1312, "wastedMs": 1242}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "totalBytes": 21531, "wastedMs": 452}]}, "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) to prioritize your page's content.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "3rd party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer size", "subItemsHeading": {"key": "transferSize"}}, {"key": "mainThreadTime", "granularity": 1, "valueType": "ms", "label": "Main thread time", "subItemsHeading": {"key": "mainThreadTime"}}], "items": [{"entity": "Google Fonts", "mainThreadTime": 0, "transferSize": 51030, "subItems": {"type": "subitems", "items": [{"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "mainThreadTime": 0, "transferSize": 48981}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "mainThreadTime": 0, "transferSize": 1312}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "mainThreadTime": 0, "transferSize": 737}]}}, {"entity": "gpteng.co", "mainThreadTime": 0, "transferSize": 232, "subItems": {"type": "subitems", "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "mainThreadTime": 0, "transferSize": 232}]}}], "isEntityGrouped": true}, "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-2-META", "path": "1,H<PERSON><PERSON>,0,HEAD,1,<PERSON><PERSON>", "selector": "head > meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">", "nodeLabel": "head > meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": null, "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.58}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 0, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 7, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 0, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 7, "group": "a11y-aria"}, {"id": "aria-text", "weight": 0, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 0, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 0, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 0, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 0, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 0, "group": "a11y-language"}, {"id": "image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "label", "weight": 7, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 0, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 7, "group": "a11y-names-labels"}, {"id": "list", "weight": 7, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 7, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 0, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 0, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 7, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 0, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 7, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 0, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 0, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 0, "group": "a11y-language"}, {"id": "video-caption", "weight": 0, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": 0.94}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": 0.96}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 1, "group": "seo-crawl"}, {"id": "image-alt", "weight": 0, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 0, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": 0.91}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "localhost", "origins": ["http://localhost:4173"], "isFirstParty": true, "isUnrecognized": true}, {"name": "gpteng.co", "origins": ["https://cdn.gpteng.co"], "isUnrecognized": true}, {"name": "Google Fonts", "homepage": "https://fonts.google.com/", "origins": ["https://fonts.googleapis.com", "https://fonts.gstatic.com"], "category": "cdn"}, {"name": "phcityrent.com", "origins": ["https://api.phcityrent.com"], "isUnrecognized": true}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 412, "height": 2986}, "nodes": {"page-0-H1": {"id": "", "top": 206, "bottom": 278, "left": 48, "right": 364, "width": 316, "height": 72}, "page-1-DIV": {"id": "", "top": 774, "bottom": 1134, "left": 16, "right": 396, "width": 380, "height": 360}, "page-2-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-3-svg": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-4-path": {"id": "", "top": 111, "bottom": 116, "left": 44, "right": 48, "width": 4, "height": 5}, "page-5-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-6-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-7-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-8-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-9-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-10-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-11-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-12-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-13-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-14-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-15-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-16-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-17-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-18-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-19-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-20-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-21-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-22-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-23-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-24-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-25-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-26-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-27-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-28-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-0-BUTTON": {"id": "", "top": 10, "bottom": 54, "left": 356, "right": 396, "width": 40, "height": 44}, "1-1-BUTTON": {"id": "", "top": 486, "bottom": 530, "left": 48, "right": 198, "width": 150, "height": 44}, "1-2-BUTTON": {"id": "", "top": 486, "bottom": 530, "left": 214, "right": 364, "width": 150, "height": 44}, "1-3-BUTTON": {"id": "", "top": 578, "bottom": 622, "left": 48, "right": 198, "width": 150, "height": 44}, "1-4-DIV": {"id": "", "top": 16, "bottom": 48, "left": 16, "right": 148, "width": 132, "height": 32}, "1-5-A": {"id": "", "top": 10, "bottom": 54, "left": 16, "right": 148, "width": 132, "height": 44}, "1-6-A": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-7-A": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-8-A": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-9-A": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-10-A": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-11-A": {"id": "", "top": 89, "bottom": 133, "left": 33, "right": 107, "width": 74, "height": 44}, "1-12-A": {"id": "", "top": 1450, "bottom": 1494, "left": 16, "right": 396, "width": 380, "height": 44}, "1-13-A": {"id": "", "top": 1506, "bottom": 1550, "left": 16, "right": 396, "width": 380, "height": 44}, "1-14-A": {"id": "", "top": 1562, "bottom": 1606, "left": 16, "right": 396, "width": 380, "height": 44}, "1-15-A": {"id": "", "top": 1682, "bottom": 1726, "left": 16, "right": 86, "width": 70, "height": 44}, "1-16-A": {"id": "", "top": 1734, "bottom": 1778, "left": 16, "right": 94, "width": 78, "height": 44}, "1-17-A": {"id": "", "top": 1786, "bottom": 1830, "left": 16, "right": 69, "width": 53, "height": 44}, "1-18-A": {"id": "", "top": 1838, "bottom": 1882, "left": 16, "right": 91, "width": 75, "height": 44}, "1-19-A": {"id": "", "top": 1958, "bottom": 2002, "left": 16, "right": 139, "width": 123, "height": 44}, "1-20-A": {"id": "", "top": 2010, "bottom": 2054, "left": 16, "right": 167, "width": 151, "height": 44}, "1-21-A": {"id": "", "top": 2062, "bottom": 2106, "left": 16, "right": 116, "width": 100, "height": 44}, "1-22-A": {"id": "", "top": 2114, "bottom": 2158, "left": 16, "right": 131, "width": 115, "height": 44}, "1-23-A": {"id": "", "top": 2234, "bottom": 2278, "left": 16, "right": 100, "width": 84, "height": 44}, "1-24-A": {"id": "", "top": 2286, "bottom": 2330, "left": 16, "right": 106, "width": 90, "height": 44}, "1-25-A": {"id": "", "top": 2338, "bottom": 2382, "left": 16, "right": 122, "width": 106, "height": 44}, "1-26-A": {"id": "", "top": 2390, "bottom": 2434, "left": 16, "right": 145, "width": 129, "height": 44}, "1-27-A": {"id": "", "top": 2878, "bottom": 2922, "left": 102, "right": 142, "width": 40, "height": 44}, "1-28-A": {"id": "", "top": 2878, "bottom": 2922, "left": 158, "right": 198, "width": 40, "height": 44}, "1-29-A": {"id": "", "top": 2878, "bottom": 2922, "left": 214, "right": 254, "width": 40, "height": 44}, "1-30-A": {"id": "", "top": 2878, "bottom": 2922, "left": 270, "right": 310, "width": 40, "height": 44}, "1-31-path": {"id": "", "top": 111, "bottom": 116, "left": 44, "right": 48, "width": 4, "height": 5}, "1-32-svg": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-33-LABEL": {"id": "", "top": 370, "bottom": 387, "left": 48, "right": 179, "width": 131, "height": 17}, "1-34-LABEL": {"id": "", "top": 370, "bottom": 387, "left": 214, "right": 349, "width": 135, "height": 17}, "1-35-LABEL": {"id": "", "top": 458, "bottom": 475, "left": 48, "right": 116, "width": 68, "height": 17}, "1-36-LABEL": {"id": "", "top": 458, "bottom": 475, "left": 214, "right": 286, "width": 72, "height": 17}, "1-37-LABEL": {"id": "", "top": 550, "bottom": 567, "left": 48, "right": 143, "width": 95, "height": 17}, "1-38-LABEL": {"id": "", "top": 550, "bottom": 567, "left": 214, "right": 272, "width": 58, "height": 17}, "1-39-INPUT": {"id": "", "top": 302, "bottom": 342, "left": 48, "right": 364, "width": 316, "height": 40}, "1-40-INPUT": {"id": "", "top": 398, "bottom": 438, "left": 48, "right": 198, "width": 150, "height": 40}, "1-41-INPUT": {"id": "", "top": 398, "bottom": 438, "left": 214, "right": 364, "width": 150, "height": 40}, "1-42-INPUT": {"id": "", "top": 578, "bottom": 618, "left": 214, "right": 364, "width": 150, "height": 40}, "1-43-INPUT": {"id": "", "top": 2631, "bottom": 2681, "left": 16, "right": 396, "width": 380, "height": 50}, "1-44-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-45-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-46-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-47-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-48-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-49-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 896.36, "name": "lh:config", "duration": 271.5, "entryType": "measure"}, {"startTime": 897.33, "name": "lh:config:resolveArtifactsToDefns", "duration": 26.01, "entryType": "measure"}, {"startTime": 1167.96, "name": "lh:runner:gather", "duration": 8282.4, "entryType": "measure"}, {"startTime": 1264.83, "name": "lh:driver:connect", "duration": 6.11, "entryType": "measure"}, {"startTime": 1271.07, "name": "lh:driver:navigate", "duration": 49.28, "entryType": "measure"}, {"startTime": 1320.64, "name": "lh:gather:getBenchmarkIndex", "duration": 1002.47, "entryType": "measure"}, {"startTime": 2323.33, "name": "lh:gather:getVersion", "duration": 0.56, "entryType": "measure"}, {"startTime": 2324.04, "name": "lh:prepare:navigationMode", "duration": 62.65, "entryType": "measure"}, {"startTime": 2337.64, "name": "lh:storage:clearDataForOrigin", "duration": 37.25, "entryType": "measure"}, {"startTime": 2375.02, "name": "lh:storage:clearBrowserCaches", "duration": 9.94, "entryType": "measure"}, {"startTime": 2385.72, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 0.94, "entryType": "measure"}, {"startTime": 2437.33, "name": "lh:driver:navigate", "duration": 4666.58, "entryType": "measure"}, {"startTime": 7385.18, "name": "lh:computed:NetworkRecords", "duration": 1.32, "entryType": "measure"}, {"startTime": 7386.83, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.07, "entryType": "measure"}, {"startTime": 7386.91, "name": "lh:gather:getArtifact:Trace", "duration": 0.02, "entryType": "measure"}, {"startTime": 7386.94, "name": "lh:gather:getArtifact:Accessibility", "duration": 159.24, "entryType": "measure"}, {"startTime": 7546.21, "name": "lh:gather:getArtifact:AnchorElements", "duration": 15.97, "entryType": "measure"}, {"startTime": 7562.2, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.08, "entryType": "measure"}, {"startTime": 7562.29, "name": "lh:gather:getArtifact:CSSUsage", "duration": 22.98, "entryType": "measure"}, {"startTime": 7585.3, "name": "lh:gather:getArtifact:Doctype", "duration": 0.95, "entryType": "measure"}, {"startTime": 7586.26, "name": "lh:gather:getArtifact:DOMStats", "duration": 2.62, "entryType": "measure"}, {"startTime": 7588.89, "name": "lh:gather:getArtifact:FontSize", "duration": 9.83, "entryType": "measure"}, {"startTime": 7598.75, "name": "lh:gather:getArtifact:Inputs", "duration": 4.11, "entryType": "measure"}, {"startTime": 7602.93, "name": "lh:gather:getArtifact:ImageElements", "duration": 8.52, "entryType": "measure"}, {"startTime": 7611.6, "name": "lh:gather:getArtifact:InspectorIssues", "duration": 0.19, "entryType": "measure"}, {"startTime": 7611.81, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.05, "entryType": "measure"}, {"startTime": 7611.87, "name": "lh:gather:getArtifact:LinkElements", "duration": 2.42, "entryType": "measure"}, {"startTime": 7614.17, "name": "lh:computed:MainResource", "duration": 0.09, "entryType": "measure"}, {"startTime": 7614.31, "name": "lh:gather:getArtifact:MainDocumentContent", "duration": 0.89, "entryType": "measure"}, {"startTime": 7615.22, "name": "lh:gather:getArtifact:MetaElements", "duration": 2.25, "entryType": "measure"}, {"startTime": 7617.5, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.1, "entryType": "measure"}, {"startTime": 7617.61, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.2, "entryType": "measure"}, {"startTime": 7617.83, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 1.68, "entryType": "measure"}, {"startTime": 7619.53, "name": "lh:gather:getArtifact:RobotsTxt", "duration": 3.19, "entryType": "measure"}, {"startTime": 7622.75, "name": "lh:gather:getArtifact:Scripts", "duration": 0.08, "entryType": "measure"}, {"startTime": 7622.85, "name": "lh:gather:getArtifact:SourceMaps", "duration": 0.05, "entryType": "measure"}, {"startTime": 7622.91, "name": "lh:gather:getArtifact:Stacks", "duration": 6.38, "entryType": "measure"}, {"startTime": 7623.22, "name": "lh:gather:collectStacks", "duration": 6.05, "entryType": "measure"}, {"startTime": 7629.29, "name": "lh:gather:getArtifact:Stylesheets", "duration": 7.8, "entryType": "measure"}, {"startTime": 7637.13, "name": "lh:gather:getArtifact:TraceElements", "duration": 197.4, "entryType": "measure"}, {"startTime": 7637.32, "name": "lh:computed:TraceEngineResult", "duration": 151.63, "entryType": "measure"}, {"startTime": 7637.37, "name": "lh:computed:ProcessedTrace", "duration": 11.59, "entryType": "measure"}, {"startTime": 7650.57, "name": "lh:computed:TraceEngineResult:total", "duration": 135.54, "entryType": "measure"}, {"startTime": 7650.63, "name": "lh:computed:TraceEngineResult:parse", "duration": 112.71, "entryType": "measure"}, {"startTime": 7651.23, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 54.69, "entryType": "measure"}, {"startTime": 7705.95, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 1.57, "entryType": "measure"}, {"startTime": 7707.83, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 1.63, "entryType": "measure"}, {"startTime": 7709.49, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 0.66, "entryType": "measure"}, {"startTime": 7710.15, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 1.35, "entryType": "measure"}, {"startTime": 7711.53, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 1.31, "entryType": "measure"}, {"startTime": 7712.86, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 2.62, "entryType": "measure"}, {"startTime": 7715.52, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 5.77, "entryType": "measure"}, {"startTime": 7721.31, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 2.42, "entryType": "measure"}, {"startTime": 7723.76, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 1.77, "entryType": "measure"}, {"startTime": 7725.55, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 1.29, "entryType": "measure"}, {"startTime": 7726.86, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 1.41, "entryType": "measure"}, {"startTime": 7728.3, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 1.56, "entryType": "measure"}, {"startTime": 7729.93, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 1.6, "entryType": "measure"}, {"startTime": 7731.55, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 9.31, "entryType": "measure"}, {"startTime": 7740.89, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 1.34, "entryType": "measure"}, {"startTime": 7742.25, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 1.28, "entryType": "measure"}, {"startTime": 7743.55, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 1.37, "entryType": "measure"}, {"startTime": 7744.95, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 1.24, "entryType": "measure"}, {"startTime": 7746.2, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 1.68, "entryType": "measure"}, {"startTime": 7747.9, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 1.29, "entryType": "measure"}, {"startTime": 7749.2, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 1.2, "entryType": "measure"}, {"startTime": 7750.41, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 2.3, "entryType": "measure"}, {"startTime": 7752.73, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 1.7, "entryType": "measure"}, {"startTime": 7754.44, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 1.19, "entryType": "measure"}, {"startTime": 7755.64, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 1.18, "entryType": "measure"}, {"startTime": 7756.83, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 1.47, "entryType": "measure"}, {"startTime": 7758.31, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 1.2, "entryType": "measure"}, {"startTime": 7759.52, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 0.22, "entryType": "measure"}, {"startTime": 7759.75, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 1.24, "entryType": "measure"}, {"startTime": 7761, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 1.32, "entryType": "measure"}, {"startTime": 7762.34, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 0.99, "entryType": "measure"}, {"startTime": 7763.35, "name": "lh:computed:TraceEngineResult:insights", "duration": 22.75, "entryType": "measure"}, {"startTime": 7763.5, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 11.53, "entryType": "measure"}, {"startTime": 7775.14, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.8, "entryType": "measure"}, {"startTime": 7775.95, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 1.39, "entryType": "measure"}, {"startTime": 7777.35, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.18, "entryType": "measure"}, {"startTime": 7777.54, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.27, "entryType": "measure"}, {"startTime": 7777.82, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 0.77, "entryType": "measure"}, {"startTime": 7778.59, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.1, "entryType": "measure"}, {"startTime": 7778.7, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.1, "entryType": "measure"}, {"startTime": 7778.81, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.14, "entryType": "measure"}, {"startTime": 7778.96, "name": "lh:computed:TraceEngineResult:insights:InteractionToNextPaint", "duration": 0.06, "entryType": "measure"}, {"startTime": 7779.02, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.1, "entryType": "measure"}, {"startTime": 7779.12, "name": "lh:computed:TraceEngineResult:insights:LCPPhases", "duration": 0.13, "entryType": "measure"}, {"startTime": 7779.26, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 0.09, "entryType": "measure"}, {"startTime": 7779.35, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 1.14, "entryType": "measure"}, {"startTime": 7780.5, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 0.92, "entryType": "measure"}, {"startTime": 7781.43, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.7, "entryType": "measure"}, {"startTime": 7782.15, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.17, "entryType": "measure"}, {"startTime": 7782.33, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 3.32, "entryType": "measure"}, {"startTime": 7785.66, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.1, "entryType": "measure"}, {"startTime": 7790.03, "name": "lh:computed:ProcessedNavigation", "duration": 0.32, "entryType": "measure"}, {"startTime": 7790.38, "name": "lh:computed:CumulativeLayoutShift", "duration": 9.34, "entryType": "measure"}, {"startTime": 7800.29, "name": "lh:computed:Responsiveness", "duration": 0.1, "entryType": "measure"}, {"startTime": 7834.55, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.77, "entryType": "measure"}, {"startTime": 7835.33, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1138.65, "entryType": "measure"}, {"startTime": 8974.02, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 455.72, "entryType": "measure"}, {"startTime": 9450.79, "name": "lh:runner:audit", "duration": 753.67, "entryType": "measure"}, {"startTime": 9451.28, "name": "lh:runner:auditing", "duration": 752.77, "entryType": "measure"}, {"startTime": 9451.99, "name": "lh:audit:is-on-https", "duration": 6.13, "entryType": "measure"}, {"startTime": 9461.6, "name": "lh:audit:redirects-http", "duration": 0.6, "entryType": "measure"}, {"startTime": 9462.34, "name": "lh:audit:viewport", "duration": 6.79, "entryType": "measure"}, {"startTime": 9462.54, "name": "lh:computed:ViewportMeta", "duration": 3.54, "entryType": "measure"}, {"startTime": 9470, "name": "lh:audit:first-contentful-paint", "duration": 60.71, "entryType": "measure"}, {"startTime": 9470.8, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 58.95, "entryType": "measure"}, {"startTime": 9523.98, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 5.76, "entryType": "measure"}, {"startTime": 9524.83, "name": "lh:computed:PageDependencyGraph", "duration": 3.69, "entryType": "measure"}, {"startTime": 9528.57, "name": "lh:computed:LoadSimulator", "duration": 0.45, "entryType": "measure"}, {"startTime": 9528.64, "name": "lh:computed:NetworkAnalysis", "duration": 0.34, "entryType": "measure"}, {"startTime": 9530.83, "name": "lh:audit:largest-contentful-paint", "duration": 1.86, "entryType": "measure"}, {"startTime": 9531.09, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 1.19, "entryType": "measure"}, {"startTime": 9531.13, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 1.14, "entryType": "measure"}, {"startTime": 9532.8, "name": "lh:audit:first-meaningful-paint", "duration": 0.37, "entryType": "measure"}, {"startTime": 9533.26, "name": "lh:audit:speed-index", "duration": 147.37, "entryType": "measure"}, {"startTime": 9533.64, "name": "lh:computed:SpeedIndex", "duration": 146.27, "entryType": "measure"}, {"startTime": 9533.73, "name": "lh:computed:LanternSpeedIndex", "duration": 146.17, "entryType": "measure"}, {"startTime": 9533.81, "name": "lh:computed:Speedline", "duration": 145.08, "entryType": "measure"}, {"startTime": 9680.67, "name": "lh:audit:screenshot-thumbnails", "duration": 0.56, "entryType": "measure"}, {"startTime": 9681.25, "name": "lh:audit:final-screenshot", "duration": 2.58, "entryType": "measure"}, {"startTime": 9683.08, "name": "lh:computed:Screenshots", "duration": 0.71, "entryType": "measure"}, {"startTime": 9684.02, "name": "lh:audit:total-blocking-time", "duration": 13.01, "entryType": "measure"}, {"startTime": 9684.24, "name": "lh:computed:TotalBlockingTime", "duration": 10.52, "entryType": "measure"}, {"startTime": 9684.29, "name": "lh:computed:LanternTotalBlockingTime", "duration": 10.46, "entryType": "measure"}, {"startTime": 9684.34, "name": "lh:computed:LanternInteractive", "duration": 6.77, "entryType": "measure"}, {"startTime": 9699, "name": "lh:audit:max-potential-fid", "duration": 58.74, "entryType": "measure"}, {"startTime": 9716.24, "name": "lh:computed:MaxPotentialFID", "duration": 36.33, "entryType": "measure"}, {"startTime": 9716.33, "name": "lh:computed:LanternMaxPotentialFID", "duration": 36.22, "entryType": "measure"}, {"startTime": 9757.87, "name": "lh:audit:cumulative-layout-shift", "duration": 0.5, "entryType": "measure"}, {"startTime": 9758.51, "name": "lh:audit:errors-in-console", "duration": 1.44, "entryType": "measure"}, {"startTime": 9758.89, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.05, "entryType": "measure"}, {"startTime": 9760.07, "name": "lh:audit:server-response-time", "duration": 0.73, "entryType": "measure"}, {"startTime": 9760.91, "name": "lh:audit:interactive", "duration": 0.55, "entryType": "measure"}, {"startTime": 9761.06, "name": "lh:computed:Interactive", "duration": 0.05, "entryType": "measure"}, {"startTime": 9761.55, "name": "lh:audit:user-timings", "duration": 0.92, "entryType": "measure"}, {"startTime": 9761.72, "name": "lh:computed:UserTimings", "duration": 0.44, "entryType": "measure"}, {"startTime": 9763.1, "name": "lh:audit:critical-request-chains", "duration": 2.33, "entryType": "measure"}, {"startTime": 9763.43, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 0.39, "entryType": "measure"}, {"startTime": 9765.64, "name": "lh:audit:redirects", "duration": 11.34, "entryType": "measure"}, {"startTime": 9777.26, "name": "lh:audit:image-aspect-ratio", "duration": 0.74, "entryType": "measure"}, {"startTime": 9778.15, "name": "lh:audit:image-size-responsive", "duration": 0.81, "entryType": "measure"}, {"startTime": 9778.38, "name": "lh:computed:ImageRecords", "duration": 0.16, "entryType": "measure"}, {"startTime": 9779.1, "name": "lh:audit:deprecations", "duration": 0.55, "entryType": "measure"}, {"startTime": 9779.78, "name": "lh:audit:third-party-cookies", "duration": 0.75, "entryType": "measure"}, {"startTime": 9780.69, "name": "lh:audit:mainthread-work-breakdown", "duration": 4.63, "entryType": "measure"}, {"startTime": 9781.01, "name": "lh:computed:MainThreadTasks", "duration": 3.38, "entryType": "measure"}, {"startTime": 9785.47, "name": "lh:audit:bootup-time", "duration": 7.65, "entryType": "measure"}, {"startTime": 9788.04, "name": "lh:computed:TBTImpactTasks", "duration": 3.62, "entryType": "measure"}, {"startTime": 9793.22, "name": "lh:audit:uses-rel-preconnect", "duration": 1.19, "entryType": "measure"}, {"startTime": 9794.54, "name": "lh:audit:font-display", "duration": 1.39, "entryType": "measure"}, {"startTime": 9795.94, "name": "lh:audit:diagnostics", "duration": 0.3, "entryType": "measure"}, {"startTime": 9796.25, "name": "lh:audit:network-requests", "duration": 0.94, "entryType": "measure"}, {"startTime": 9796.34, "name": "lh:computed:EntityClassification", "duration": 0.66, "entryType": "measure"}, {"startTime": 9797.29, "name": "lh:audit:network-rtt", "duration": 0.76, "entryType": "measure"}, {"startTime": 9798.17, "name": "lh:audit:network-server-latency", "duration": 0.52, "entryType": "measure"}, {"startTime": 9798.7, "name": "lh:audit:main-thread-tasks", "duration": 0.25, "entryType": "measure"}, {"startTime": 9798.97, "name": "lh:audit:metrics", "duration": 1.55, "entryType": "measure"}, {"startTime": 9799.03, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 1.36, "entryType": "measure"}, {"startTime": 9799.17, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.15, "entryType": "measure"}, {"startTime": 9799.34, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.05, "entryType": "measure"}, {"startTime": 9799.42, "name": "lh:computed:LCPBreakdown", "duration": 0.63, "entryType": "measure"}, {"startTime": 9799.46, "name": "lh:computed:TimeToFirstByte", "duration": 0.07, "entryType": "measure"}, {"startTime": 9799.53, "name": "lh:computed:LCPImageRecord", "duration": 0.52, "entryType": "measure"}, {"startTime": 9800.52, "name": "lh:audit:resource-summary", "duration": 0.63, "entryType": "measure"}, {"startTime": 9800.59, "name": "lh:computed:ResourceSummary", "duration": 0.22, "entryType": "measure"}, {"startTime": 9801.29, "name": "lh:audit:third-party-summary", "duration": 2.23, "entryType": "measure"}, {"startTime": 9803.64, "name": "lh:audit:third-party-facades", "duration": 1.56, "entryType": "measure"}, {"startTime": 9805.29, "name": "lh:audit:largest-contentful-paint-element", "duration": 0.66, "entryType": "measure"}, {"startTime": 9806.06, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.42, "entryType": "measure"}, {"startTime": 9806.58, "name": "lh:audit:layout-shifts", "duration": 0.57, "entryType": "measure"}, {"startTime": 9807.24, "name": "lh:audit:long-tasks", "duration": 1.91, "entryType": "measure"}, {"startTime": 9809.26, "name": "lh:audit:non-composited-animations", "duration": 0.65, "entryType": "measure"}, {"startTime": 9810.07, "name": "lh:audit:unsized-images", "duration": 0.58, "entryType": "measure"}, {"startTime": 9810.77, "name": "lh:audit:valid-source-maps", "duration": 0.63, "entryType": "measure"}, {"startTime": 9811.49, "name": "lh:audit:prioritize-lcp-image", "duration": 0.34, "entryType": "measure"}, {"startTime": 9811.93, "name": "lh:audit:csp-xss", "duration": 0.47, "entryType": "measure"}, {"startTime": 9812.49, "name": "lh:audit:has-hsts", "duration": 3.11, "entryType": "measure"}, {"startTime": 9815.72, "name": "lh:audit:origin-isolation", "duration": 0.7, "entryType": "measure"}, {"startTime": 9816.53, "name": "lh:audit:clickjacking-mitigation", "duration": 0.46, "entryType": "measure"}, {"startTime": 9817, "name": "lh:audit:script-treemap-data", "duration": 34.56, "entryType": "measure"}, {"startTime": 9817.16, "name": "lh:computed:ModuleDuplication", "duration": 0.08, "entryType": "measure"}, {"startTime": 9817.25, "name": "lh:computed:UnusedJavascriptSummary", "duration": 34.21, "entryType": "measure"}, {"startTime": 9851.75, "name": "lh:audit:accesskeys", "duration": 0.81, "entryType": "measure"}, {"startTime": 9852.7, "name": "lh:audit:aria-allowed-attr", "duration": 1.58, "entryType": "measure"}, {"startTime": 9854.41, "name": "lh:audit:aria-allowed-role", "duration": 1.56, "entryType": "measure"}, {"startTime": 9856.1, "name": "lh:audit:aria-command-name", "duration": 0.43, "entryType": "measure"}, {"startTime": 9856.63, "name": "lh:audit:aria-conditional-attr", "duration": 2.38, "entryType": "measure"}, {"startTime": 9859.14, "name": "lh:audit:aria-deprecated-role", "duration": 1.45, "entryType": "measure"}, {"startTime": 9860.75, "name": "lh:audit:aria-dialog-name", "duration": 0.43, "entryType": "measure"}, {"startTime": 9861.29, "name": "lh:audit:aria-hidden-body", "duration": 1.36, "entryType": "measure"}, {"startTime": 9862.74, "name": "lh:audit:aria-hidden-focus", "duration": 1.42, "entryType": "measure"}, {"startTime": 9864.27, "name": "lh:audit:aria-input-field-name", "duration": 0.43, "entryType": "measure"}, {"startTime": 9864.8, "name": "lh:audit:aria-meter-name", "duration": 0.46, "entryType": "measure"}, {"startTime": 9865.35, "name": "lh:audit:aria-progressbar-name", "duration": 0.49, "entryType": "measure"}, {"startTime": 9865.94, "name": "lh:audit:aria-prohibited-attr", "duration": 1.39, "entryType": "measure"}, {"startTime": 9867.47, "name": "lh:audit:aria-required-attr", "duration": 1.69, "entryType": "measure"}, {"startTime": 9869.31, "name": "lh:audit:aria-required-children", "duration": 0.65, "entryType": "measure"}, {"startTime": 9870.07, "name": "lh:audit:aria-required-parent", "duration": 0.56, "entryType": "measure"}, {"startTime": 9870.72, "name": "lh:audit:aria-roles", "duration": 1.26, "entryType": "measure"}, {"startTime": 9872.08, "name": "lh:audit:aria-text", "duration": 0.57, "entryType": "measure"}, {"startTime": 9872.75, "name": "lh:audit:aria-toggle-field-name", "duration": 0.6, "entryType": "measure"}, {"startTime": 9873.46, "name": "lh:audit:aria-tooltip-name", "duration": 0.63, "entryType": "measure"}, {"startTime": 9874.19, "name": "lh:audit:aria-treeitem-name", "duration": 3, "entryType": "measure"}, {"startTime": 9877.33, "name": "lh:audit:aria-valid-attr-value", "duration": 1.59, "entryType": "measure"}, {"startTime": 9879.03, "name": "lh:audit:aria-valid-attr", "duration": 1.45, "entryType": "measure"}, {"startTime": 9880.59, "name": "lh:audit:button-name", "duration": 1.44, "entryType": "measure"}, {"startTime": 9882.13, "name": "lh:audit:bypass", "duration": 1.32, "entryType": "measure"}, {"startTime": 9883.57, "name": "lh:audit:color-contrast", "duration": 1.37, "entryType": "measure"}, {"startTime": 9885.05, "name": "lh:audit:definition-list", "duration": 0.94, "entryType": "measure"}, {"startTime": 9886.11, "name": "lh:audit:dlitem", "duration": 0.71, "entryType": "measure"}, {"startTime": 9889.93, "name": "lh:audit:document-title", "duration": 1.57, "entryType": "measure"}, {"startTime": 9891.64, "name": "lh:audit:duplicate-id-aria", "duration": 0.71, "entryType": "measure"}, {"startTime": 9892.48, "name": "lh:audit:empty-heading", "duration": 1.32, "entryType": "measure"}, {"startTime": 9893.9, "name": "lh:audit:form-field-multiple-labels", "duration": 1.31, "entryType": "measure"}, {"startTime": 9895.31, "name": "lh:audit:frame-title", "duration": 0.7, "entryType": "measure"}, {"startTime": 9896.1, "name": "lh:audit:heading-order", "duration": 1.36, "entryType": "measure"}, {"startTime": 9897.56, "name": "lh:audit:html-has-lang", "duration": 1.41, "entryType": "measure"}, {"startTime": 9899.07, "name": "lh:audit:html-lang-valid", "duration": 1.56, "entryType": "measure"}, {"startTime": 9900.78, "name": "lh:audit:html-xml-lang-mismatch", "duration": 0.88, "entryType": "measure"}, {"startTime": 9901.78, "name": "lh:audit:identical-links-same-purpose", "duration": 4.07, "entryType": "measure"}, {"startTime": 9906, "name": "lh:audit:image-alt", "duration": 0.88, "entryType": "measure"}, {"startTime": 9906.99, "name": "lh:audit:image-redundant-alt", "duration": 0.97, "entryType": "measure"}, {"startTime": 9908.08, "name": "lh:audit:input-button-name", "duration": 0.85, "entryType": "measure"}, {"startTime": 9909.03, "name": "lh:audit:input-image-alt", "duration": 0.96, "entryType": "measure"}, {"startTime": 9910.11, "name": "lh:audit:label-content-name-mismatch", "duration": 1.01, "entryType": "measure"}, {"startTime": 9911.23, "name": "lh:audit:label", "duration": 1.32, "entryType": "measure"}, {"startTime": 9912.64, "name": "lh:audit:landmark-one-main", "duration": 1.25, "entryType": "measure"}, {"startTime": 9913.99, "name": "lh:audit:link-name", "duration": 1.32, "entryType": "measure"}, {"startTime": 9915.41, "name": "lh:audit:link-in-text-block", "duration": 0.91, "entryType": "measure"}, {"startTime": 9916.42, "name": "lh:audit:list", "duration": 7.4, "entryType": "measure"}, {"startTime": 9924.12, "name": "lh:audit:listitem", "duration": 4.31, "entryType": "measure"}, {"startTime": 9928.66, "name": "lh:audit:meta-refresh", "duration": 2.54, "entryType": "measure"}, {"startTime": 9931.49, "name": "lh:audit:meta-viewport", "duration": 3.63, "entryType": "measure"}, {"startTime": 9935.48, "name": "lh:audit:object-alt", "duration": 4.38, "entryType": "measure"}, {"startTime": 9939.99, "name": "lh:audit:select-name", "duration": 1.27, "entryType": "measure"}, {"startTime": 9941.39, "name": "lh:audit:skip-link", "duration": 1.77, "entryType": "measure"}, {"startTime": 9943.49, "name": "lh:audit:tabindex", "duration": 1.52, "entryType": "measure"}, {"startTime": 9945.13, "name": "lh:audit:table-duplicate-name", "duration": 1.39, "entryType": "measure"}, {"startTime": 9946.62, "name": "lh:audit:table-fake-caption", "duration": 1.18, "entryType": "measure"}, {"startTime": 9947.9, "name": "lh:audit:target-size", "duration": 1.3, "entryType": "measure"}, {"startTime": 9949.3, "name": "lh:audit:td-has-header", "duration": 1.23, "entryType": "measure"}, {"startTime": 9950.63, "name": "lh:audit:td-headers-attr", "duration": 1.39, "entryType": "measure"}, {"startTime": 9952.13, "name": "lh:audit:th-has-data-cells", "duration": 1.26, "entryType": "measure"}, {"startTime": 9953.49, "name": "lh:audit:valid-lang", "duration": 1.22, "entryType": "measure"}, {"startTime": 9954.81, "name": "lh:audit:video-caption", "duration": 1.22, "entryType": "measure"}, {"startTime": 9956.04, "name": "lh:audit:custom-controls-labels", "duration": 0.07, "entryType": "measure"}, {"startTime": 9956.12, "name": "lh:audit:custom-controls-roles", "duration": 0.02, "entryType": "measure"}, {"startTime": 9956.14, "name": "lh:audit:focus-traps", "duration": 0.01, "entryType": "measure"}, {"startTime": 9956.15, "name": "lh:audit:focusable-controls", "duration": 0.01, "entryType": "measure"}, {"startTime": 9956.17, "name": "lh:audit:interactive-element-affordance", "duration": 0.01, "entryType": "measure"}, {"startTime": 9956.19, "name": "lh:audit:logical-tab-order", "duration": 0.01, "entryType": "measure"}, {"startTime": 9956.2, "name": "lh:audit:managed-focus", "duration": 0.01, "entryType": "measure"}, {"startTime": 9956.22, "name": "lh:audit:offscreen-content-hidden", "duration": 0.01, "entryType": "measure"}, {"startTime": 9956.24, "name": "lh:audit:use-landmarks", "duration": 0.02, "entryType": "measure"}, {"startTime": 9956.26, "name": "lh:audit:visual-order-follows-dom", "duration": 0.02, "entryType": "measure"}, {"startTime": 9956.38, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.15, "entryType": "measure"}, {"startTime": 9957.62, "name": "lh:audit:total-byte-weight", "duration": 0.8, "entryType": "measure"}, {"startTime": 9958.54, "name": "lh:audit:offscreen-images", "duration": 4.4, "entryType": "measure"}, {"startTime": 9963.06, "name": "lh:audit:render-blocking-resources", "duration": 7.39, "entryType": "measure"}, {"startTime": 9963.47, "name": "lh:computed:UnusedCSS", "duration": 5.66, "entryType": "measure"}, {"startTime": 9969.18, "name": "lh:computed:NavigationInsights", "duration": 0.13, "entryType": "measure"}, {"startTime": 9969.37, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.05, "entryType": "measure"}, {"startTime": 9970.54, "name": "lh:audit:unminified-css", "duration": 11, "entryType": "measure"}, {"startTime": 9981.63, "name": "lh:audit:unminified-javascript", "duration": 76.72, "entryType": "measure"}, {"startTime": 10058.46, "name": "lh:audit:unused-css-rules", "duration": 1.96, "entryType": "measure"}, {"startTime": 10060.5, "name": "lh:audit:unused-javascript", "duration": 1.95, "entryType": "measure"}, {"startTime": 10062.55, "name": "lh:audit:modern-image-formats", "duration": 1.48, "entryType": "measure"}, {"startTime": 10064.11, "name": "lh:audit:uses-optimized-images", "duration": 1.44, "entryType": "measure"}, {"startTime": 10065.64, "name": "lh:audit:uses-text-compression", "duration": 1.27, "entryType": "measure"}, {"startTime": 10066.98, "name": "lh:audit:uses-responsive-images", "duration": 1.04, "entryType": "measure"}, {"startTime": 10068.11, "name": "lh:audit:efficient-animated-content", "duration": 1.25, "entryType": "measure"}, {"startTime": 10069.44, "name": "lh:audit:duplicated-javascript", "duration": 1.13, "entryType": "measure"}, {"startTime": 10070.66, "name": "lh:audit:legacy-javascript", "duration": 103.43, "entryType": "measure"}, {"startTime": 10174.23, "name": "lh:audit:doctype", "duration": 0.57, "entryType": "measure"}, {"startTime": 10174.91, "name": "lh:audit:charset", "duration": 0.61, "entryType": "measure"}, {"startTime": 10175.69, "name": "lh:audit:dom-size", "duration": 1.5, "entryType": "measure"}, {"startTime": 10177.35, "name": "lh:audit:geolocation-on-start", "duration": 0.5, "entryType": "measure"}, {"startTime": 10177.97, "name": "lh:audit:inspector-issues", "duration": 0.4, "entryType": "measure"}, {"startTime": 10178.47, "name": "lh:audit:no-document-write", "duration": 0.37, "entryType": "measure"}, {"startTime": 10178.92, "name": "lh:audit:js-libraries", "duration": 0.29, "entryType": "measure"}, {"startTime": 10179.32, "name": "lh:audit:notification-on-start", "duration": 0.38, "entryType": "measure"}, {"startTime": 10179.8, "name": "lh:audit:paste-preventing-inputs", "duration": 0.35, "entryType": "measure"}, {"startTime": 10180.23, "name": "lh:audit:uses-http2", "duration": 1.57, "entryType": "measure"}, {"startTime": 10181.95, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.55, "entryType": "measure"}, {"startTime": 10182.62, "name": "lh:audit:meta-description", "duration": 0.38, "entryType": "measure"}, {"startTime": 10183.1, "name": "lh:audit:http-status-code", "duration": 0.37, "entryType": "measure"}, {"startTime": 10183.58, "name": "lh:audit:font-size", "duration": 0.67, "entryType": "measure"}, {"startTime": 10184.36, "name": "lh:audit:link-text", "duration": 0.59, "entryType": "measure"}, {"startTime": 10185.06, "name": "lh:audit:crawlable-anchors", "duration": 0.62, "entryType": "measure"}, {"startTime": 10185.79, "name": "lh:audit:is-crawlable", "duration": 0.75, "entryType": "measure"}, {"startTime": 10186.64, "name": "lh:audit:robots-txt", "duration": 0.83, "entryType": "measure"}, {"startTime": 10187.58, "name": "lh:audit:hreflang", "duration": 0.4, "entryType": "measure"}, {"startTime": 10188.08, "name": "lh:audit:canonical", "duration": 0.41, "entryType": "measure"}, {"startTime": 10188.57, "name": "lh:audit:structured-data", "duration": 0.24, "entryType": "measure"}, {"startTime": 10188.91, "name": "lh:audit:bf-cache", "duration": 2.62, "entryType": "measure"}, {"startTime": 10191.81, "name": "lh:audit:cache-insight", "duration": 1.09, "entryType": "measure"}, {"startTime": 10193.02, "name": "lh:audit:cls-culprits-insight", "duration": 0.91, "entryType": "measure"}, {"startTime": 10194.04, "name": "lh:audit:document-latency-insight", "duration": 0.4, "entryType": "measure"}, {"startTime": 10194.55, "name": "lh:audit:dom-size-insight", "duration": 0.48, "entryType": "measure"}, {"startTime": 10195.13, "name": "lh:audit:duplicated-javascript-insight", "duration": 0.39, "entryType": "measure"}, {"startTime": 10195.61, "name": "lh:audit:font-display-insight", "duration": 0.37, "entryType": "measure"}, {"startTime": 10196.09, "name": "lh:audit:forced-reflow-insight", "duration": 0.39, "entryType": "measure"}, {"startTime": 10196.57, "name": "lh:audit:image-delivery-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 10197.01, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 10197.46, "name": "lh:audit:lcp-discovery-insight", "duration": 0.33, "entryType": "measure"}, {"startTime": 10197.88, "name": "lh:audit:lcp-phases-insight", "duration": 0.44, "entryType": "measure"}, {"startTime": 10198.43, "name": "lh:audit:legacy-javascript-insight", "duration": 0.46, "entryType": "measure"}, {"startTime": 10198.99, "name": "lh:audit:modern-http-insight", "duration": 0.36, "entryType": "measure"}, {"startTime": 10199.44, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.44, "entryType": "measure"}, {"startTime": 10200, "name": "lh:audit:render-blocking-insight", "duration": 0.43, "entryType": "measure"}, {"startTime": 10200.53, "name": "lh:audit:third-parties-insight", "duration": 3.06, "entryType": "measure"}, {"startTime": 10203.69, "name": "lh:audit:viewport-insight", "duration": 0.36, "entryType": "measure"}, {"startTime": 10204.06, "name": "lh:runner:generate", "duration": 0.39, "entryType": "measure"}], "total": 9036.07}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 6905.0301}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 7776.0301}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 6905.0301}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 7776.0301}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 707.2559999999971}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 302.4119999999996}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 140}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 149}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 166.277}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 90.26499999999999}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 7776.0301}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | failureTitle": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/lib/i18n/i18n.js | columnSource": ["audits[errors-in-console].details.headings[0].label", "audits[font-size].details.headings[0].label"], "core/lib/i18n/i18n.js | columnDescription": ["audits[errors-in-console].details.headings[1].label", "audits[csp-xss].details.headings[0].label", "audits[has-hsts].details.headings[0].label", "audits[origin-isolation].details.headings[0].label", "audits[clickjacking-mitigation].details.headings[0].label"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 0.7949999999999999}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits[bootup-time].details.headings[0].label", "audits[uses-rel-preconnect].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[long-tasks].details.headings[0].label", "audits[valid-source-maps].details.headings[0].label", "audits[uses-long-cache-ttl].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[render-blocking-resources].details.headings[0].label", "audits[unused-css-rules].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[legacy-javascript].details.headings[0].label", "audits[render-blocking-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/critical-request-chains.js | displayValue": [{"values": {"itemCount": 4}, "path": "audits[critical-request-chains].displayValue"}], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/lib/i18n/i18n.js | displayValueMsSavings": [{"values": {"wastedMs": 632.5051}, "path": "audits[uses-rel-preconnect].displayValue"}, {"values": {"wastedMs": 1500}, "path": "audits[render-blocking-resources].displayValue"}], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[uses-rel-preconnect].details.headings[1].label", "audits[render-blocking-resources].details.headings[2].label", "audits[unused-css-rules].details.headings[2].label", "audits[unused-javascript].details.headings[2].label", "audits[legacy-javascript].details.headings[2].label", "audits[render-blocking-insight].details.headings[2].label"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[third-party-summary].details.headings[1].label", "audits[uses-long-cache-ttl].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[render-blocking-resources].details.headings[1].label", "audits[unused-css-rules].details.headings[1].label", "audits[unused-javascript].details.headings[1].label", "audits[cache-insight].details.headings[2].label", "audits[render-blocking-insight].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label", "audits[cls-culprits-insight].details.items[0].items[0].node.value"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-summary.js | displayValue": [{"values": {"timeInMs": 0}, "path": "audits[third-party-summary].displayValue"}], "core/audits/third-party-summary.js | columnThirdParty": ["audits[third-party-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnBlockingTime": ["audits[third-party-summary].details.headings[2].label"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[layout-shifts].details.headings[0].label", "audits[dom-size].details.headings[1].label", "audits[cls-culprits-insight].details.items[0].headings[0].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/layout-shifts.js | displayValueShiftsFound": [{"values": {"shiftCount": 1}, "path": "audits[layout-shifts].displayValue"}], "core/audits/layout-shifts.js | columnScore": ["audits[layout-shifts].details.headings[1].label"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/long-tasks.js | displayValue": [{"values": {"itemCount": 3}, "path": "audits[long-tasks].displayValue"}], "core/lib/i18n/i18n.js | columnStartTime": ["audits[long-tasks].details.headings[1].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[long-tasks].details.headings[2].label", "audits[lcp-phases-insight].details.items[0].headings[1].label"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | failureTitle": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/valid-source-maps.js | columnMapURL": ["audits[valid-source-maps].details.headings[1].label"], "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": ["audits[valid-source-maps].details.items[0].subItems.items[0].error"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/csp-xss.js | columnDirective": ["audits[csp-xss].details.headings[1].label"], "core/audits/csp-xss.js | columnSeverity": ["audits[csp-xss].details.headings[2].label"], "core/lib/i18n/i18n.js | itemSeverityHigh": ["audits[csp-xss].details.items[0].severity", "audits[has-hsts].details.items[0].severity", "audits[origin-isolation].details.items[0].severity", "audits[clickjacking-mitigation].details.items[0].severity"], "core/audits/csp-xss.js | noCsp": ["audits[csp-xss].details.items[0].description"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/has-hsts.js | columnDirective": ["audits[has-hsts].details.headings[1].label"], "core/audits/has-hsts.js | columnSeverity": ["audits[has-hsts].details.headings[2].label"], "core/audits/has-hsts.js | noHsts": ["audits[has-hsts].details.items[0].description"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/origin-isolation.js | columnDirective": ["audits[origin-isolation].details.headings[1].label"], "core/audits/origin-isolation.js | columnSeverity": ["audits[origin-isolation].details.headings[2].label"], "core/audits/origin-isolation.js | noCoop": ["audits[origin-isolation].details.items[0].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/clickjacking-mitigation.js | columnSeverity": ["audits[clickjacking-mitigation].details.headings[1].label"], "core/audits/clickjacking-mitigation.js | noClickjackingMitigation": ["audits[clickjacking-mitigation].details.items[0].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | failureTitle": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/lib/i18n/i18n.js | columnFailingElem": ["audits[button-name].details.headings[0].label"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | title": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | title": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 1}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/lib/i18n/i18n.js | columnCacheTTL": ["audits[uses-long-cache-ttl].details.headings[1].label", "audits[cache-insight].details.headings[1].label"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 894763}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 19252}, "path": "audits[unused-css-rules].displayValue"}, {"values": {"wastedBytes": 628218}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 586}, "path": "audits[legacy-javascript].displayValue"}, {"values": {"wastedBytes": 157.76}, "path": "audits[cache-insight].displayValue"}], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 210}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/font-size.js | displayValue": [{"values": {"decimalProportion": 1}, "path": "audits[font-size].displayValue"}], "core/audits/seo/font-size.js | columnSelector": ["audits[font-size].details.headings[1].label"], "core/audits/seo/font-size.js | columnPercentPageText": ["audits[font-size].details.headings[2].label"], "core/audits/seo/font-size.js | columnFontSize": ["audits[font-size].details.headings[3].label"], "core/audits/seo/font-size.js | legibleText": ["audits[font-size].details.items[0].source.value"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | failureTitle": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/robots-txt.js | displayValueValidationError": [{"values": {"itemCount": 18}, "path": "audits[robots-txt].displayValue"}], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | title": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | requestColumn": ["audits[cache-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "core/audits/insights/cls-culprits-insight.js | columnScore": ["audits[cls-culprits-insight].details.items[0].headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": ["audits[document-latency-insight].details.items.noRedirects.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": [{"values": {"PH1": "1 ms"}, "path": "audits[document-latency-insight].details.items.serverResponseIsFast.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase": ["audits[lcp-phases-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte": ["audits[lcp-phases-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay": ["audits[lcp-phases-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnThirdParty": ["audits[third-parties-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnTransferSize": ["audits[third-parties-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnMainThreadTime": ["audits[third-parties-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}