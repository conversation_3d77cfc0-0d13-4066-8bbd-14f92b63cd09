import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Globe,
  Server,
  Zap,
  RefreshCw,
  TrendingUp,
  Users,
  FileText,
} from 'lucide-react';

interface HealthMetric {
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  value: string;
  lastChecked: string;
  description: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'good' | 'warning' | 'critical';
}

interface SystemStatus {
  overall: 'healthy' | 'warning' | 'critical';
  uptime: string;
  version: string;
  environment: string;
  lastDeployment: string;
}

const HealthDashboard: React.FC = () => {
  const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Mock data - replace with actual API calls
  useEffect(() => {
    fetchHealthData();
    const interval = setInterval(fetchHealthData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchHealthData = async () => {
    setIsLoading(true);

    // Simulate API calls - replace with actual endpoints
    try {
      // Mock health metrics
      const mockHealthMetrics: HealthMetric[] = [
        {
          name: 'API Server',
          status: 'healthy',
          value: 'Online',
          lastChecked: new Date().toISOString(),
          description: 'Main API server is responding normally',
        },
        {
          name: 'Database',
          status: 'healthy',
          value: 'Connected',
          lastChecked: new Date().toISOString(),
          description: 'Database connection is stable',
        },
        {
          name: 'File Storage',
          status: 'healthy',
          value: 'Available',
          lastChecked: new Date().toISOString(),
          description: 'File storage service is operational',
        },
        {
          name: 'Payment Gateway',
          status: 'warning',
          value: 'Slow Response',
          lastChecked: new Date().toISOString(),
          description: 'Payment gateway responding slower than usual',
        },
        {
          name: 'Email Service',
          status: 'healthy',
          value: 'Operational',
          lastChecked: new Date().toISOString(),
          description: 'Email service is working normally',
        },
        {
          name: 'CDN',
          status: 'healthy',
          value: 'Active',
          lastChecked: new Date().toISOString(),
          description: 'Content delivery network is active',
        },
      ];

      // Mock performance metrics
      const mockPerformanceMetrics: PerformanceMetric[] = [
        {
          name: 'Response Time',
          value: 245,
          unit: 'ms',
          threshold: 500,
          status: 'good',
        },
        {
          name: 'Error Rate',
          value: 0.2,
          unit: '%',
          threshold: 1,
          status: 'good',
        },
        {
          name: 'CPU Usage',
          value: 45,
          unit: '%',
          threshold: 80,
          status: 'good',
        },
        {
          name: 'Memory Usage',
          value: 62,
          unit: '%',
          threshold: 85,
          status: 'good',
        },
        {
          name: 'Disk Usage',
          value: 78,
          unit: '%',
          threshold: 90,
          status: 'warning',
        },
        {
          name: 'Active Users',
          value: 1247,
          unit: 'users',
          threshold: 5000,
          status: 'good',
        },
      ];

      // Mock system status
      const mockSystemStatus: SystemStatus = {
        overall: 'healthy',
        uptime: '15 days, 7 hours',
        version: '1.2.3',
        environment: import.meta.env.VITE_ENVIRONMENT || 'development',
        lastDeployment: '2024-01-15 14:30:00',
      };

      setHealthMetrics(mockHealthMetrics);
      setPerformanceMetrics(mockPerformanceMetrics);
      setSystemStatus(mockSystemStatus);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      healthy: 'default',
      good: 'default',
      warning: 'secondary',
      critical: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  if (isLoading && !systemStatus) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading health data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Health Dashboard</h1>
          <p className="text-muted-foreground">Last updated: {lastRefresh.toLocaleTimeString()}</p>
        </div>
        <Button onClick={fetchHealthData} disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Overview */}
      {systemStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              System Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {getStatusIcon(systemStatus.overall)}
                </div>
                <p className="text-sm font-medium">Overall Status</p>
                <p className="text-xs text-muted-foreground">{systemStatus.overall}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                </div>
                <p className="text-sm font-medium">Uptime</p>
                <p className="text-xs text-muted-foreground">{systemStatus.uptime}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <FileText className="h-4 w-4 text-purple-500" />
                </div>
                <p className="text-sm font-medium">Version</p>
                <p className="text-xs text-muted-foreground">v{systemStatus.version}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Globe className="h-4 w-4 text-green-500" />
                </div>
                <p className="text-sm font-medium">Environment</p>
                <p className="text-xs text-muted-foreground">{systemStatus.environment}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Activity className="h-4 w-4 text-orange-500" />
                </div>
                <p className="text-sm font-medium">Last Deploy</p>
                <p className="text-xs text-muted-foreground">
                  {new Date(systemStatus.lastDeployment).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Metrics */}
      <Tabs defaultValue="health" className="space-y-4">
        <TabsList>
          <TabsTrigger value="health">Health Checks</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="logs">Recent Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="health" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {healthMetrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      {getStatusIcon(metric.status)}
                      {metric.name}
                    </span>
                    {getStatusBadge(metric.status)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium">{metric.value}</p>
                  <p className="text-xs text-muted-foreground mt-1">{metric.description}</p>
                  <p className="text-xs text-muted-foreground mt-2">
                    Last checked: {new Date(metric.lastChecked).toLocaleTimeString()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {performanceMetrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      {metric.name}
                    </span>
                    {getStatusBadge(metric.status)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-baseline gap-1">
                    <span className="text-2xl font-bold">{metric.value}</span>
                    <span className="text-sm text-muted-foreground">{metric.unit}</span>
                  </div>
                  <div className="mt-2">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>
                        Threshold: {metric.threshold}
                        {metric.unit}
                      </span>
                      <span>{((metric.value / metric.threshold) * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className={`h-2 rounded-full ${
                          metric.status === 'good'
                            ? 'bg-green-500'
                            : metric.status === 'warning'
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                        }`}
                        style={{
                          width: `${Math.min((metric.value / metric.threshold) * 100, 100)}%`,
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent System Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 font-mono text-sm">
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span className="text-gray-500">2024-01-15 15:30:45</span>
                  <span>Health check passed for all services</span>
                </div>
                <div className="flex items-center gap-2 text-blue-600">
                  <Activity className="h-3 w-3" />
                  <span className="text-gray-500">2024-01-15 15:25:12</span>
                  <span>Performance metrics updated</span>
                </div>
                <div className="flex items-center gap-2 text-yellow-600">
                  <AlertTriangle className="h-3 w-3" />
                  <span className="text-gray-500">2024-01-15 15:20:33</span>
                  <span>Payment gateway response time increased</span>
                </div>
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span className="text-gray-500">2024-01-15 15:15:21</span>
                  <span>Database connection pool optimized</span>
                </div>
                <div className="flex items-center gap-2 text-blue-600">
                  <Users className="h-3 w-3" />
                  <span className="text-gray-500">2024-01-15 15:10:45</span>
                  <span>1,247 active users detected</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HealthDashboard;
