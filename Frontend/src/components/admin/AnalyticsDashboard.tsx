import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  Eye, 
  Clock, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  Activity,
  Globe,
  Smartphone,
  Monitor,
  RefreshCw
} from 'lucide-react';

interface AnalyticsData {
  realTimeUsers: number;
  pageViews: number;
  sessionDuration: number;
  bounceRate: number;
  conversionRate: number;
  errorRate: number;
}

interface PerformanceData {
  timestamp: string;
  responseTime: number;
  throughput: number;
  errorCount: number;
}

interface UserBehaviorData {
  page: string;
  views: number;
  avgTime: number;
  bounceRate: number;
}

interface DeviceData {
  device: string;
  users: number;
  percentage: number;
}

const AnalyticsDashboard: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [userBehaviorData, setUserBehaviorData] = useState<UserBehaviorData[]>([]);
  const [deviceData, setDeviceData] = useState<DeviceData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Mock data - replace with actual analytics API calls
  useEffect(() => {
    fetchAnalyticsData();
    const interval = setInterval(fetchAnalyticsData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchAnalyticsData = async () => {
    setIsLoading(true);
    
    try {
      // Mock real-time analytics data
      const mockAnalytics: AnalyticsData = {
        realTimeUsers: Math.floor(Math.random() * 150) + 50,
        pageViews: Math.floor(Math.random() * 5000) + 2000,
        sessionDuration: Math.floor(Math.random() * 300) + 120, // seconds
        bounceRate: Math.random() * 0.4 + 0.3, // 30-70%
        conversionRate: Math.random() * 0.05 + 0.02, // 2-7%
        errorRate: Math.random() * 0.02, // 0-2%
      };

      // Mock performance data (last 24 hours)
      const mockPerformanceData: PerformanceData[] = Array.from({ length: 24 }, (_, i) => ({
        timestamp: `${23 - i}:00`,
        responseTime: Math.floor(Math.random() * 500) + 200,
        throughput: Math.floor(Math.random() * 1000) + 500,
        errorCount: Math.floor(Math.random() * 10),
      }));

      // Mock user behavior data
      const mockUserBehaviorData: UserBehaviorData[] = [
        { page: 'Home', views: 1250, avgTime: 45, bounceRate: 0.35 },
        { page: 'Properties', views: 980, avgTime: 120, bounceRate: 0.25 },
        { page: 'Property Details', views: 650, avgTime: 180, bounceRate: 0.20 },
        { page: 'Contact', views: 320, avgTime: 90, bounceRate: 0.45 },
        { page: 'About', views: 180, avgTime: 60, bounceRate: 0.55 },
        { page: 'Auth', views: 150, avgTime: 30, bounceRate: 0.40 },
      ];

      // Mock device data
      const mockDeviceData: DeviceData[] = [
        { device: 'Mobile', users: 850, percentage: 65 },
        { device: 'Desktop', users: 350, percentage: 27 },
        { device: 'Tablet', users: 104, percentage: 8 },
      ];

      setAnalyticsData(mockAnalytics);
      setPerformanceData(mockPerformanceData);
      setUserBehaviorData(mockUserBehaviorData);
      setDeviceData(mockDeviceData);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  if (isLoading && !analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading analytics data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time insights and performance metrics
          </p>
          <p className="text-sm text-muted-foreground">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>
        <Button onClick={fetchAnalyticsData} disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      {analyticsData && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Real-time Users</p>
                  <p className="text-2xl font-bold">{analyticsData.realTimeUsers}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Page Views</p>
                  <p className="text-2xl font-bold">{analyticsData.pageViews.toLocaleString()}</p>
                </div>
                <Eye className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg. Session</p>
                  <p className="text-2xl font-bold">{formatDuration(analyticsData.sessionDuration)}</p>
                </div>
                <Clock className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Bounce Rate</p>
                  <p className={`text-2xl font-bold ${getStatusColor(analyticsData.bounceRate, { good: 0.3, warning: 0.5 })}`}>
                    {formatPercentage(analyticsData.bounceRate)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Conversion</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatPercentage(analyticsData.conversionRate)}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Error Rate</p>
                  <p className={`text-2xl font-bold ${getStatusColor(analyticsData.errorRate, { good: 0.01, warning: 0.02 })}`}>
                    {formatPercentage(analyticsData.errorRate)}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Analytics */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="behavior">User Behavior</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Response Time (24h)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="responseTime" 
                      stroke="#3B82F6" 
                      strokeWidth={2}
                      name="Response Time (ms)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Throughput & Errors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="throughput" 
                      stackId="1"
                      stroke="#10B981" 
                      fill="#10B981"
                      name="Throughput"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="errorCount" 
                      stackId="2"
                      stroke="#EF4444" 
                      fill="#EF4444"
                      name="Errors"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="behavior" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Page Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={userBehaviorData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="page" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="views" fill="#3B82F6" name="Page Views" />
                  <Bar dataKey="avgTime" fill="#10B981" name="Avg Time (s)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Device Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={deviceData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ device, percentage }) => `${device} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="users"
                    >
                      {deviceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Device Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {deviceData.map((device, index) => (
                    <div key={device.device} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {device.device === 'Mobile' && <Smartphone className="h-5 w-5" />}
                        {device.device === 'Desktop' && <Monitor className="h-5 w-5" />}
                        {device.device === 'Tablet' && <Globe className="h-5 w-5" />}
                        <span className="font-medium">{device.device}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{device.users.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">{device.percentage}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {analyticsData?.realTimeUsers || 0}
                  </div>
                  <p className="text-sm text-muted-foreground">Active Users</p>
                  <Badge variant="outline" className="mt-2">
                    Live
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {Math.floor(Math.random() * 50) + 10}
                  </div>
                  <p className="text-sm text-muted-foreground">Pages/Session</p>
                  <Badge variant="outline" className="mt-2">
                    Avg
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    {Math.floor(Math.random() * 100) + 50}%
                  </div>
                  <p className="text-sm text-muted-foreground">New Visitors</p>
                  <Badge variant="outline" className="mt-2">
                    Today
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">
                    {Math.floor(Math.random() * 10) + 5}
                  </div>
                  <p className="text-sm text-muted-foreground">Conversions</p>
                  <Badge variant="outline" className="mt-2">
                    Last Hour
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard;
