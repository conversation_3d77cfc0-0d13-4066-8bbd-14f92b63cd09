// Advanced Monitoring and Observability Setup
import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

// Performance monitoring interface
interface PerformanceMetrics {
  name: string;
  value: number;
  timestamp: number;
  url: string;
  userAgent: string;
}

// Error tracking interface
interface ErrorReport {
  message: string;
  stack?: string;
  url: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  buildVersion: string;
}

// User analytics interface
interface UserEvent {
  event: string;
  properties: Record<string, any>;
  userId?: string;
  sessionId: string;
  timestamp: number;
}

class MonitoringService {
  private sessionId: string;
  private buildVersion: string;
  private isInitialized: boolean = false;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.buildVersion = import.meta.env.VITE_APP_VERSION || '1.0.0';
  }

  // Initialize monitoring services
  initialize() {
    if (this.isInitialized) return;

    this.initializeSentry();
    this.initializePerformanceMonitoring();
    this.initializeErrorTracking();
    this.initializeUserAnalytics();

    this.isInitialized = true;
    console.log('🔍 Monitoring services initialized');
  }

  // Initialize Sentry for error tracking
  private initializeSentry() {
    if (!import.meta.env.VITE_SENTRY_DSN) return;

    Sentry.init({
      dsn: import.meta.env.VITE_SENTRY_DSN,
      environment: import.meta.env.VITE_SENTRY_ENVIRONMENT || 'development',
      release: this.buildVersion,
      integrations: [
        new BrowserTracing({
          // Set sampling rate for performance monitoring
          tracePropagationTargets: [
            'localhost',
            /^https:\/\/api\.phcityrent\.com/,
            /^https:\/\/staging-api\.phcityrent\.com/,
          ],
        }),
      ],
      // Performance monitoring sample rate
      tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0,
      // Session replay sample rate
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      beforeSend: event => {
        // Filter out development errors in production
        if (import.meta.env.PROD && event.exception) {
          const error = event.exception.values?.[0];
          if (error?.value?.includes('Development')) {
            return null;
          }
        }
        return event;
      },
    });
  }

  // Initialize performance monitoring
  private initializePerformanceMonitoring() {
    // Core Web Vitals monitoring
    this.observeWebVitals();

    // Custom performance metrics
    this.observeCustomMetrics();

    // Resource loading monitoring
    this.observeResourceLoading();
  }

  // Observe Core Web Vitals
  private observeWebVitals() {
    // First Contentful Paint (FCP)
    this.observePerformanceEntry('first-contentful-paint', entry => {
      this.reportMetric({
        name: 'FCP',
        value: entry.startTime,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', entry => {
      this.reportMetric({
        name: 'LCP',
        value: entry.startTime,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Cumulative Layout Shift (CLS)
    this.observeLayoutShift();

    // First Input Delay (FID)
    this.observeFirstInputDelay();
  }

  // Observe performance entries
  private observePerformanceEntry(entryType: string, callback: (entry: any) => void) {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        list.getEntries().forEach(callback);
      });
      observer.observe({ entryTypes: [entryType] });
    }
  }

  // Observe layout shift
  private observeLayoutShift() {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }

        this.reportMetric({
          name: 'CLS',
          value: clsValue,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        });
      });
      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  // Observe first input delay
  private observeFirstInputDelay() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          this.reportMetric({
            name: 'FID',
            value: (entry as any).processingStart - entry.startTime,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
          });
        });
      });
      observer.observe({ entryTypes: ['first-input'] });
    }
  }

  // Observe custom metrics
  private observeCustomMetrics() {
    // Time to Interactive (TTI)
    this.measureTimeToInteractive();

    // Bundle size metrics
    this.measureBundleSize();

    // API response times
    this.measureApiResponseTimes();
  }

  // Measure time to interactive
  private measureTimeToInteractive() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        this.reportMetric({
          name: 'TTI',
          value: performance.now(),
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        });
      }, 0);
    });
  }

  // Measure bundle size
  private measureBundleSize() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        let totalSize = 0;
        list.getEntries().forEach(entry => {
          if (entry.name.includes('.js') || entry.name.includes('.css')) {
            totalSize += (entry as any).transferSize || 0;
          }
        });

        if (totalSize > 0) {
          this.reportMetric({
            name: 'BundleSize',
            value: totalSize,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
          });
        }
      });
      observer.observe({ entryTypes: ['resource'] });
    }
  }

  // Measure API response times
  private measureApiResponseTimes() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const response = await originalFetch(...args);
      const endTime = performance.now();

      const url = typeof args[0] === 'string' ? args[0] : args[0].url;
      if (url.includes('/api/')) {
        this.reportMetric({
          name: 'APIResponseTime',
          value: endTime - startTime,
          timestamp: Date.now(),
          url: url,
          userAgent: navigator.userAgent,
        });
      }

      return response;
    };
  }

  // Observe resource loading
  private observeResourceLoading() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          if ((entry as any).transferSize > 1000000) {
            // > 1MB
            this.reportMetric({
              name: 'LargeResource',
              value: (entry as any).transferSize,
              timestamp: Date.now(),
              url: entry.name,
              userAgent: navigator.userAgent,
            });
          }
        });
      });
      observer.observe({ entryTypes: ['resource'] });
    }
  }

  // Initialize error tracking
  private initializeErrorTracking() {
    // Global error handler
    window.addEventListener('error', event => {
      this.reportError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        buildVersion: this.buildVersion,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', event => {
      this.reportError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        buildVersion: this.buildVersion,
      });
    });
  }

  // Initialize user analytics
  private initializeUserAnalytics() {
    // Page view tracking
    this.trackPageView();

    // User interaction tracking
    this.trackUserInteractions();

    // Performance impact on user experience
    this.trackUserExperienceMetrics();
  }

  // Track page views
  private trackPageView() {
    this.trackEvent('page_view', {
      url: window.location.href,
      title: document.title,
      referrer: document.referrer,
    });
  }

  // Track user interactions
  private trackUserInteractions() {
    // Click tracking
    document.addEventListener('click', event => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.tagName === 'A') {
        this.trackEvent('click', {
          element: target.tagName,
          text: target.textContent?.slice(0, 100),
          url: window.location.href,
        });
      }
    });
  }

  // Track user experience metrics
  private trackUserExperienceMetrics() {
    // Rage clicks detection
    let clickCount = 0;
    let clickTimer: NodeJS.Timeout;

    document.addEventListener('click', () => {
      clickCount++;
      clearTimeout(clickTimer);

      clickTimer = setTimeout(() => {
        if (clickCount > 5) {
          this.trackEvent('rage_click', {
            clicks: clickCount,
            url: window.location.href,
          });
        }
        clickCount = 0;
      }, 1000);
    });
  }

  // Report performance metric
  private reportMetric(metric: PerformanceMetrics) {
    if (import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true') {
      // Send to monitoring service
      this.sendToMonitoringService('metrics', metric);
    }
  }

  // Report error
  private reportError(error: ErrorReport) {
    if (import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true') {
      // Send to error tracking service
      this.sendToMonitoringService('errors', error);

      // Also send to Sentry
      Sentry.captureException(new Error(error.message));
    }
  }

  // Track user event
  public trackEvent(event: string, properties: Record<string, any> = {}) {
    if (import.meta.env.VITE_ENABLE_ANALYTICS === 'true') {
      const userEvent: UserEvent = {
        event,
        properties,
        sessionId: this.sessionId,
        timestamp: Date.now(),
      };

      this.sendToMonitoringService('events', userEvent);
    }
  }

  // Send data to monitoring service
  private sendToMonitoringService(type: string, data: any) {
    // In a real implementation, you would send this to your monitoring service
    // Examples: New Relic, DataDog, custom analytics endpoint

    if (import.meta.env.DEV) {
      console.log(`📊 Monitoring [${type}]:`, data);
    }

    // Example implementation:
    // fetch('/api/v1/monitoring', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ type, data })
    // }).catch(console.error);
  }

  // Generate session ID
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public methods for manual tracking
  public setUser(userId: string) {
    Sentry.setUser({ id: userId });
  }

  public addBreadcrumb(message: string, category: string = 'custom') {
    Sentry.addBreadcrumb({
      message,
      category,
      timestamp: Date.now() / 1000,
    });
  }

  public captureException(error: Error) {
    this.reportError({
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      buildVersion: this.buildVersion,
    });
  }
}

// Export singleton instance
export const monitoring = new MonitoringService();

// Initialize monitoring when module is imported
if (typeof window !== 'undefined') {
  monitoring.initialize();
}
