
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './useAuth';

export const useAuthRedirect = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (loading || !user) return;

    // Use backend user info for role-based redirect
    const role = user.role?.toUpperCase();
    if (role === 'ADMIN' || role === 'SUPER_ADMIN') {
      navigate('/admin');
    } else if (role === 'AGENT') {
      navigate('/enhanced-agent-dashboard');
    } else if (role === 'LANDLORD') {
      navigate('/landlord-portal');
    } else {
      navigate('/tenant-portal');
    }
  }, [user, loading, navigate]);
};
