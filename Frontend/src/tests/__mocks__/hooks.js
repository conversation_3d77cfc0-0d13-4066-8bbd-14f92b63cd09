// Mock for custom hooks

// Mock useImageOptimization hook
const useImageOptimization = jest.fn(() => ({
  optimizeImage: jest.fn().mockResolvedValue('optimized-image-url'),
  isOptimizing: false,
  error: null,
}));

// Mock useAuth hook
const useAuth = jest.fn(() => ({
  user: null,
  loading: false,
  isAuthenticated: false,
  isAdmin: false,
  signIn: jest.fn().mockResolvedValue({ success: true }),
  signUp: jest.fn().mockResolvedValue({ success: true }),
  signOut: jest.fn().mockResolvedValue({ success: true }),
  resetPassword: jest.fn().mockResolvedValue({ success: true }),
}));

// Mock useLocalStorage hook
const useLocalStorage = jest.fn((key, initialValue) => [
  initialValue,
  jest.fn(),
  jest.fn(),
]);

// Mock useDebounce hook
const useDebounce = jest.fn((value, delay) => value);

// Mock useIntersectionObserver hook
const useIntersectionObserver = jest.fn(() => ({
  ref: { current: null },
  isIntersecting: false,
  entry: null,
}));

// Mock useMediaQuery hook
const useMediaQuery = jest.fn(() => false);

// Mock useOnClickOutside hook
const useOnClickOutside = jest.fn();

// Mock useKeyPress hook
const useKeyPress = jest.fn(() => false);

module.exports = {
  useImageOptimization,
  useAuth,
  useLocalStorage,
  useDebounce,
  useIntersectionObserver,
  useMediaQuery,
  useOnClickOutside,
  useKeyPress,
};
