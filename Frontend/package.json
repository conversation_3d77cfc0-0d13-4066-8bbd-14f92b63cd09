{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' jest --config jest.config.cjs", "test:watch": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' jest --config jest.config.cjs --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' jest --config jest.config.cjs --coverage", "test:unit": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' jest --config config/jest.config.cjs --testPathPatterns=unit", "test:integration": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' jest --config config/jest.config.cjs --testPathPatterns=integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:performance": "playwright test --grep=\"Performance\"", "test:mobile": "playwright test --grep=\"Mobile\"", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' npm run test:coverage && npm run test:e2e", "test:debug": "NODE_OPTIONS='--experimental-vm-modules --experimental-global-webcrypto --experimental-websocket' jest --config jest.config.cjs --detectOpenHandles --forceExit", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps", "test:setup": "npm run playwright:install && npm run playwright:install-deps", "type-check": "tsc --noEmit", "lint:fix": "eslint . --fix", "prepare": "cd .. && husky install frontend/.husky", "analyze:bundle": "npx vite-bundle-analyzer dist/stats.json", "build:analyze": "vite build --mode analyze", "lighthouse": "lhci autorun", "performance:audit": "npm run build && npm run lighthouse"}, "dependencies": {"@capacitor/android": "^7.4.0", "@capacitor/cli": "^7.4.0", "@capacitor/core": "^7.4.0", "@capacitor/ios": "^7.4.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sendgrid/mail": "^8.1.5", "@sentry/react": "^9.40.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^5.56.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "embla-carousel-react": "^8.3.0", "express-rate-limit": "^7.5.1", "framer-motion": "^12.23.0", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "intersection-observer": "^0.12.2", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.0", "lucide-react": "^0.462.0", "mapbox-gl": "^3.12.0", "next-themes": "^0.3.0", "nodemailer": "^7.0.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.15.4", "resend": "^4.6.0", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "twilio": "^5.7.2", "vaul": "^0.9.3", "web-vitals": "^5.0.3", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.6", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@eslint/js": "^9.9.0", "@lhci/cli": "^0.15.1", "@playwright/test": "^1.53.2", "@swc/core": "^1.12.11", "@swc/jest": "^0.2.39", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "babel-jest": "^30.0.4", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^30.0.4", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^3.0.1", "jsdom": "^26.1.0", "lint-staged": "^15.2.0", "lovable-tagger": "^1.1.3", "msw": "^2.10.3", "node-notifier": "^10.0.1", "postcss": "^8.4.47", "prettier": "^3.1.1", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.11", "ts-jest": "^29.4.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "util": "^0.12.5", "vite": "^5.4.1", "vite-bundle-analyzer": "^1.1.0", "whatwg-fetch": "^3.6.20"}}