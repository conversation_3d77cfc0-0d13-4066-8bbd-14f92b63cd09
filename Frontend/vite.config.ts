import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { componentTagger } from 'lovable-tagger';
import { visualizer } from 'rollup-plugin-visualizer';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: '::',
    port: 8080,
    hmr: {
      overlay: false, // Disable error overlay that might cause issues
    },
    watch: {
      usePolling: false, // Disable polling to reduce file system load
    },
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
    // Bundle analyzer - only in analyze mode
    mode === 'analyze' &&
      visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
      }),
  ].filter(<PERSON><PERSON>an),
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
}));
