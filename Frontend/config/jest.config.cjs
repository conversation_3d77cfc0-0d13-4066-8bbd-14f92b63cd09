// =====================================================
// JEST CONFIGURATION
// Comprehensive testing configuration for PHCityRent
// =====================================================

/** @type {import('jest').Config} */
module.exports = {
  // Root directory
  rootDir: '..',

  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files after env
  setupFilesAfterEnv: [
    '<rootDir>/src/tests/setup/test-setup.ts'
  ],
  
  // Module name mapper
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/src/tests/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/tests/__mocks__/fileMock.js',
    '^import\\.meta$': '<rootDir>/src/tests/__mocks__/importMeta.js',
    '^@/integrations/supabase/client$': '<rootDir>/src/tests/__mocks__/supabase.js',
    '^@/lib/supabase$': '<rootDir>/src/tests/__mocks__/supabase.js',
    '^@/hooks/useImageOptimization$': '<rootDir>/src/tests/__mocks__/hooks.js',
    '^@/hooks/useAuth$': '<rootDir>/src/tests/__mocks__/hooks.js'
  },

  // Transform configuration
  transform: {
    '^.+\\.(t|j)sx?$': ['@swc/jest', {
      jsc: {
        parser: {
          syntax: 'typescript',
          tsx: true,
          decorators: true
        },
        transform: {
          react: {
            runtime: 'automatic'
          }
        }
      }
    }]
  },

  // Test match patterns
  testMatch: [
    '<rootDir>/src/tests/**/*.test.{ts,tsx}'
  ],

  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/tests/**/*',
    '!src/**/*.d.ts'
  ],

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Test environment configuration
  testEnvironmentOptions: {
    url: 'http://localhost'
  },

  // Global configuration
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    },
    'import.meta': {
      env: {
        VITE_API_BASE_URL: 'http://localhost:3001/api/v1',
        VITE_SUPABASE_URL: 'https://test.supabase.co',
        VITE_SUPABASE_ANON_KEY: 'test-key',
        VITE_ENVIRONMENT: 'test',
        MODE: 'test',
        DEV: false,
        PROD: false,
        SSR: false
      }
    }
  },

  // Ignore patterns
  transformIgnorePatterns: [
    '/node_modules/(?!(@supabase|@tanstack|@radix-ui)/)'
  ]
}; 