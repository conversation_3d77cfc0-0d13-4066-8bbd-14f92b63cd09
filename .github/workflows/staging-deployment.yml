name: 🚀 Staging Deployment

on:
  push:
    branches: [develop, staging]
  pull_request:
    branches: [develop, staging]
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '18.x'
  STAGING_URL: 'https://staging.phcityrent.com'

jobs:
  # ==========================================
  # PRE-DEPLOYMENT VALIDATION
  # ==========================================
  validate:
    name: 🔍 Pre-deployment Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Run Linting
        run: |
          cd frontend
          npm run lint

      - name: Run Type Checking
        run: |
          cd frontend
          npm run type-check

      - name: Run Unit Tests
        run: |
          cd frontend
          npm run test:unit

      - name: Run Integration Tests
        run: |
          cd frontend
          npm run test:integration
        continue-on-error: true

      - name: Security Audit
        run: |
          cd frontend
          npm audit --audit-level=moderate

  # ==========================================
  # BUILD FOR STAGING
  # ==========================================
  build-staging:
    name: 🏗️ Build Staging
    runs-on: ubuntu-latest
    needs: [validate]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Build for Staging
        run: |
          cd frontend
          npm run build
        env:
          VITE_NODE_ENV: staging
          VITE_API_BASE_URL: ${{ secrets.STAGING_API_BASE_URL }}
          VITE_SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}
          VITE_PAYSTACK_PUBLIC_KEY: ${{ secrets.STAGING_PAYSTACK_PUBLIC_KEY }}
          VITE_GOOGLE_MAPS_API_KEY: ${{ secrets.STAGING_GOOGLE_MAPS_API_KEY }}
          VITE_SENTRY_DSN: ${{ secrets.STAGING_SENTRY_DSN }}

      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v3
        with:
          name: staging-build
          path: frontend/dist/
          retention-days: 7

      - name: Generate Build Report
        run: |
          cd frontend
          echo "## 📊 Staging Build Report" >> $GITHUB_STEP_SUMMARY
          echo "- **Build Time**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- **Node Version**: ${{ env.NODE_VERSION }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Build Size**: $(du -sh dist/ | cut -f1)" >> $GITHUB_STEP_SUMMARY
          echo "- **Files Count**: $(find dist/ -type f | wc -l)" >> $GITHUB_STEP_SUMMARY

  # ==========================================
  # PERFORMANCE TESTING
  # ==========================================
  performance-test:
    name: ⚡ Performance Testing
    runs-on: ubuntu-latest
    needs: [build-staging]
    if: github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Download Build Artifacts
        uses: actions/download-artifact@v3
        with:
          name: staging-build
          path: frontend/dist/

      - name: Run Lighthouse CI
        run: |
          cd frontend
          npm run lighthouse
        continue-on-error: true

      - name: Upload Lighthouse Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: lighthouse-staging-results
          path: frontend/.lighthouseci/
          retention-days: 30

  # ==========================================
  # DEPLOY TO STAGING
  # ==========================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-staging, performance-test]
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/staging'
    environment:
      name: staging
      url: ${{ env.STAGING_URL }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Build Artifacts
        uses: actions/download-artifact@v3
        with:
          name: staging-build
          path: frontend/dist/

      - name: Deploy to Staging Server
        run: |
          echo "🚀 Deploying to staging environment..."
          echo "📦 Deployment package ready"
          echo "🌐 Staging URL: ${{ env.STAGING_URL }}"
          # Add your actual deployment commands here
          # Examples:
          # - AWS S3 + CloudFront
          # - Netlify
          # - Vercel
          # - Custom server deployment

      - name: Health Check
        run: |
          echo "🏥 Running health checks..."
          # Add health check commands
          # curl -f ${{ env.STAGING_URL }}/health || exit 1

      - name: Notify Deployment Success
        run: |
          echo "## ✅ Staging Deployment Successful" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: Staging" >> $GITHUB_STEP_SUMMARY
          echo "- **URL**: ${{ env.STAGING_URL }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Deployed At**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

  # ==========================================
  # POST-DEPLOYMENT TESTING
  # ==========================================
  post-deployment-tests:
    name: 🧪 Post-deployment Tests
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/staging'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Run E2E Tests Against Staging
        run: |
          cd frontend
          npm run test:e2e
        env:
          PLAYWRIGHT_BASE_URL: ${{ env.STAGING_URL }}
        continue-on-error: true

      - name: Upload E2E Test Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-staging-results
          path: frontend/test-results/
          retention-days: 30

      - name: Smoke Tests
        run: |
          echo "🔥 Running smoke tests..."
          # Add smoke test commands
          # curl -f ${{ env.STAGING_URL }} || exit 1
          # curl -f ${{ env.STAGING_URL }}/api/health || exit 1

  # ==========================================
  # NOTIFICATION
  # ==========================================
  notify:
    name: 📢 Notify Team
    runs-on: ubuntu-latest
    needs: [deploy-staging, post-deployment-tests]
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.deploy-staging.result == 'success'
        run: |
          echo "✅ Staging deployment completed successfully!"
          echo "🌐 Staging URL: ${{ env.STAGING_URL }}"
          # Add notification logic (Slack, Discord, Email, etc.)

      - name: Notify Failure
        if: needs.deploy-staging.result == 'failure'
        run: |
          echo "❌ Staging deployment failed!"
          echo "🔍 Check the logs for details"
          # Add failure notification logic
