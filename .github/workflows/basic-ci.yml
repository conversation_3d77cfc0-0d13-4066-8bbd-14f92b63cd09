name: Basic CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'

jobs:
  # ==========================================
  # LINT AND TYPE CHECK
  # ==========================================
  lint-and-typecheck:
    name: Lint & Type Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            frontend/package-lock.json
            backend/package-lock.json

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm ci

      - name: Lint Frontend
        run: |
          cd frontend
          npm run lint

      - name: Type Check Frontend
        run: |
          cd frontend
          npm run type-check

      - name: Install Backend Dependencies
        run: |
          cd backend
          npm ci

      - name: Lint Backend
        run: |
          cd backend
          npm run lint || echo "Backend lint not configured yet"

  # ==========================================
  # SECURITY SCAN
  # ==========================================
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm ci

      - name: Install Backend Dependencies
        run: |
          cd backend
          npm ci

      - name: Run Security Audit - Frontend
        run: |
          cd frontend
          npm audit --audit-level=moderate || true

      - name: Run Security Audit - Backend
        run: |
          cd backend
          npm audit --audit-level=moderate || true

      - name: Dependency Vulnerability Scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Secrets Scanning
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  # ==========================================
  # BUILD TEST
  # ==========================================
  build:
    name: Build Test
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            frontend/package-lock.json
            backend/package-lock.json

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm ci

      - name: Build Frontend
        run: |
          cd frontend
          npm run build

      - name: Install Backend Dependencies
        run: |
          cd backend
          npm ci

      - name: Build Backend
        run: |
          cd backend
          npm run build || echo "Backend build not configured yet"

      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            frontend/dist/
            backend/dist/
          retention-days: 7

  # ==========================================
  # BASIC TESTS
  # ==========================================
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm ci

      - name: Run Frontend Tests
        run: |
          cd frontend
          npm run test:coverage

      - name: Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          directory: frontend/coverage
          flags: frontend
          fail_ci_if_error: false

  # ==========================================
  # PERFORMANCE MONITORING
  # ==========================================
  performance-audit:
    name: Performance Audit
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm ci

      - name: Build for Performance Analysis
        run: |
          cd frontend
          npm run build:analyze

      - name: Lighthouse CI
        run: |
          cd frontend
          npm run lighthouse
        continue-on-error: true

      - name: Upload Lighthouse Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: lighthouse-results
          path: frontend/.lighthouseci/
          retention-days: 7

      - name: Bundle Size Analysis
        run: |
          cd frontend
          npm run analyze:bundle
        continue-on-error: true

  # ==========================================
  # NOTIFICATION
  # ==========================================
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, security-scan, build, test]
    if: always()
    
    steps:
      - name: Check job results
        run: |
          echo "Lint & Type Check: ${{ needs.lint-and-typecheck.result }}"
          echo "Security Scan: ${{ needs.security-scan.result }}"
          echo "Build: ${{ needs.build.result }}"
          echo "Test: ${{ needs.test.result }}"
          
          if [[ "${{ needs.lint-and-typecheck.result }}" == "success" && 
                "${{ needs.security-scan.result }}" == "success" && 
                "${{ needs.build.result }}" == "success" && 
                "${{ needs.test.result }}" == "success" ]]; then
            echo "✅ All checks passed!"
          else
            echo "❌ Some checks failed!"
            exit 1
          fi
