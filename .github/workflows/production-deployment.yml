name: 🚀 Production Deployment

on:
  push:
    branches: [main]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - hotfix
      rollback:
        description: 'Rollback to previous version'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18.x'
  PRODUCTION_URL: 'https://phcityrent.com'
  STAGING_SLOT_URL: 'https://staging-slot.phcityrent.com'

jobs:
  # ==========================================
  # PRE-PRODUCTION VALIDATION
  # ==========================================
  pre-production-validation:
    name: 🔍 Pre-production Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Run Full Test Suite
        run: |
          cd frontend
          npm run test:all

      - name: Security Vulnerability Scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Security Scan Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Performance Budget Check
        run: |
          cd frontend
          npm run build
          npm run lighthouse
        continue-on-error: true

  # ==========================================
  # BUILD PRODUCTION
  # ==========================================
  build-production:
    name: 🏗️ Build Production
    runs-on: ubuntu-latest
    needs: [pre-production-validation]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Build for Production
        run: |
          cd frontend
          npm run build
        env:
          VITE_NODE_ENV: production
          VITE_API_BASE_URL: ${{ secrets.PROD_API_BASE_URL }}
          VITE_SUPABASE_URL: ${{ secrets.PROD_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.PROD_SUPABASE_ANON_KEY }}
          VITE_PAYSTACK_PUBLIC_KEY: ${{ secrets.PROD_PAYSTACK_PUBLIC_KEY }}
          VITE_GOOGLE_MAPS_API_KEY: ${{ secrets.PROD_GOOGLE_MAPS_API_KEY }}
          VITE_SENTRY_DSN: ${{ secrets.PROD_SENTRY_DSN }}

      - name: Generate Build Manifest
        run: |
          cd frontend
          echo "{
            \"version\": \"${{ github.sha }}\",
            \"buildTime\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
            \"environment\": \"production\",
            \"nodeVersion\": \"${{ env.NODE_VERSION }}\",
            \"buildSize\": \"$(du -sh dist/ | cut -f1)\",
            \"filesCount\": $(find dist/ -type f | wc -l)
          }" > dist/build-manifest.json

      - name: Upload Production Build
        uses: actions/upload-artifact@v3
        with:
          name: production-build
          path: frontend/dist/
          retention-days: 30

  # ==========================================
  # BLUE-GREEN DEPLOYMENT
  # ==========================================
  deploy-to-staging-slot:
    name: 🟦 Deploy to Staging Slot (Blue-Green)
    runs-on: ubuntu-latest
    needs: [build-production]
    environment:
      name: production-staging-slot
      url: ${{ env.STAGING_SLOT_URL }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Production Build
        uses: actions/download-artifact@v3
        with:
          name: production-build
          path: frontend/dist/

      - name: Deploy to Staging Slot
        run: |
          echo "🟦 Deploying to staging slot (blue environment)..."
          echo "📦 Production build ready for staging slot"
          echo "🌐 Staging Slot URL: ${{ env.STAGING_SLOT_URL }}"
          # Add your staging slot deployment commands here

      - name: Staging Slot Health Check
        run: |
          echo "🏥 Running staging slot health checks..."
          # Add health check commands for staging slot
          # curl -f ${{ env.STAGING_SLOT_URL }}/health || exit 1

  # ==========================================
  # PRODUCTION VALIDATION
  # ==========================================
  validate-staging-slot:
    name: ✅ Validate Staging Slot
    runs-on: ubuntu-latest
    needs: [deploy-to-staging-slot]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Dependencies
        run: |
          cd frontend
          npm ci

      - name: Run E2E Tests on Staging Slot
        run: |
          cd frontend
          npm run test:e2e
        env:
          PLAYWRIGHT_BASE_URL: ${{ env.STAGING_SLOT_URL }}

      - name: Performance Testing on Staging Slot
        run: |
          cd frontend
          npm run lighthouse
        env:
          LIGHTHOUSE_URL: ${{ env.STAGING_SLOT_URL }}

      - name: Load Testing
        run: |
          echo "🔄 Running load tests on staging slot..."
          # Add load testing commands
          # k6 run load-test.js --env BASE_URL=${{ env.STAGING_SLOT_URL }}

  # ==========================================
  # PRODUCTION DEPLOYMENT (GREEN)
  # ==========================================
  deploy-to-production:
    name: 🟢 Deploy to Production (Green)
    runs-on: ubuntu-latest
    needs: [validate-staging-slot]
    environment:
      name: production
      url: ${{ env.PRODUCTION_URL }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Production Build
        uses: actions/download-artifact@v3
        with:
          name: production-build
          path: frontend/dist/

      - name: Backup Current Production
        run: |
          echo "💾 Creating backup of current production..."
          # Add backup commands
          # aws s3 sync s3://prod-bucket s3://backup-bucket/$(date +%Y%m%d-%H%M%S)/

      - name: Deploy to Production
        run: |
          echo "🟢 Deploying to production (green environment)..."
          echo "📦 Production deployment starting..."
          echo "🌐 Production URL: ${{ env.PRODUCTION_URL }}"
          # Add your production deployment commands here

      - name: Production Health Check
        run: |
          echo "🏥 Running production health checks..."
          # Add production health check commands
          # curl -f ${{ env.PRODUCTION_URL }}/health || exit 1

      - name: Warm Up Production
        run: |
          echo "🔥 Warming up production environment..."
          # Add warm-up commands
          # curl -s ${{ env.PRODUCTION_URL }} > /dev/null

  # ==========================================
  # POST-DEPLOYMENT MONITORING
  # ==========================================
  post-deployment-monitoring:
    name: 📊 Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-to-production]
    
    steps:
      - name: Monitor Application Health
        run: |
          echo "📊 Starting post-deployment monitoring..."
          # Add monitoring setup
          # curl -X POST "https://api.newrelic.com/v2/applications/APP_ID/deployments.json" \
          #   -H "X-Api-Key: ${{ secrets.NEW_RELIC_API_KEY }}" \
          #   -d '{"deployment": {"revision": "${{ github.sha }}"}}'

      - name: Update Error Tracking
        run: |
          echo "🐛 Updating error tracking with new release..."
          # curl -sL https://sentry.io/get-cli/ | bash
          # sentry-cli releases new ${{ github.sha }}
          # sentry-cli releases set-commits --auto ${{ github.sha }}

      - name: Performance Baseline
        run: |
          echo "⚡ Establishing performance baseline..."
          # Add performance monitoring setup

  # ==========================================
  # ROLLBACK CAPABILITY
  # ==========================================
  rollback:
    name: 🔄 Rollback
    runs-on: ubuntu-latest
    if: github.event.inputs.rollback == 'true' || failure()
    needs: [deploy-to-production]
    environment:
      name: production
      url: ${{ env.PRODUCTION_URL }}
    
    steps:
      - name: Rollback to Previous Version
        run: |
          echo "🔄 Rolling back to previous version..."
          # Add rollback commands
          # aws s3 sync s3://backup-bucket/latest/ s3://prod-bucket/

      - name: Verify Rollback
        run: |
          echo "✅ Verifying rollback..."
          # Add rollback verification
          # curl -f ${{ env.PRODUCTION_URL }}/health || exit 1

  # ==========================================
  # NOTIFICATION
  # ==========================================
  notify:
    name: 📢 Notify Team
    runs-on: ubuntu-latest
    needs: [deploy-to-production, post-deployment-monitoring]
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.deploy-to-production.result == 'success'
        run: |
          echo "🎉 Production deployment completed successfully!"
          echo "🌐 Production URL: ${{ env.PRODUCTION_URL }}"
          echo "📊 Monitoring active"
          # Add success notification logic

      - name: Notify Failure
        if: needs.deploy-to-production.result == 'failure'
        run: |
          echo "🚨 Production deployment failed!"
          echo "🔄 Rollback may be required"
          # Add failure notification logic
